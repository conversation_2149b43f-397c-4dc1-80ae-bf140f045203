// State management utilities for Legal Contract Generator

import { saveToLocalStorage, loadFromLocalStorage, clearLocalStorage } from './localStorage';

/**
 * Contract state manager for handling form state, drafts, and persistence
 */
export class ContractStateManager {
  constructor() {
    this.state = {
      currentStep: 'selection',
      selectedContractType: null,
      formData: {},
      generatedContract: '',
      error: null,
      isGenerating: false,
      validationErrors: {},
      aiSuggestions: {},
      formHistory: [],
      autoSaveEnabled: true,
      lastSaved: null
    };
    
    this.listeners = [];
    this.autoSaveInterval = null;
    this.initializeAutoSave();
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  notify() {
    this.listeners.forEach(listener => listener(this.state));
  }

  /**
   * Update state and notify listeners
   */
  setState(updates) {
    const prevState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    // Save to history for undo functionality
    if (updates.formData && JSON.stringify(prevState.formData) !== JSON.stringify(updates.formData)) {
      this.addToHistory(prevState.formData);
    }
    
    this.notify();
    
    // Auto-save if enabled
    if (this.state.autoSaveEnabled && (updates.formData || updates.selectedContractType)) {
      this.autoSave();
    }
  }

  /**
   * Get current state
   */
  getState() {
    return { ...this.state };
  }

  /**
   * Initialize auto-save functionality
   */
  initializeAutoSave() {
    // Auto-save every 30 seconds
    this.autoSaveInterval = setInterval(() => {
      if (this.state.autoSaveEnabled && this.hasUnsavedChanges()) {
        this.autoSave();
      }
    }, 30000);
  }

  /**
   * Check if there are unsaved changes
   */
  hasUnsavedChanges() {
    const saved = loadFromLocalStorage('legalContractDraft');
    if (!saved) return Object.keys(this.state.formData).length > 0;
    
    return JSON.stringify(saved.formData) !== JSON.stringify(this.state.formData) ||
           saved.contractType !== this.state.selectedContractType;
  }

  /**
   * Auto-save current state
   */
  autoSave() {
    if (this.state.selectedContractType || Object.keys(this.state.formData).length > 0) {
      saveToLocalStorage('legalContractDraft', {
        contractType: this.state.selectedContractType,
        formData: this.state.formData,
        currentStep: this.state.currentStep,
        timestamp: Date.now()
      });
      
      this.setState({ lastSaved: new Date().toISOString() });
    }
  }

  /**
   * Load saved state from localStorage
   */
  loadSavedState() {
    const saved = loadFromLocalStorage('legalContractDraft');
    if (saved) {
      this.setState({
        selectedContractType: saved.contractType,
        formData: saved.formData || {},
        currentStep: saved.currentStep || 'form'
      });
      return true;
    }
    return false;
  }

  /**
   * Clear saved state
   */
  clearSavedState() {
    clearLocalStorage('legalContractDraft');
    this.setState({
      currentStep: 'selection',
      selectedContractType: null,
      formData: {},
      generatedContract: '',
      error: null,
      validationErrors: {},
      aiSuggestions: {}
    });
  }

  /**
   * Add form data to history for undo functionality
   */
  addToHistory(formData) {
    const history = [...this.state.formHistory];
    history.push({
      formData: { ...formData },
      timestamp: Date.now()
    });
    
    // Keep only last 10 states
    if (history.length > 10) {
      history.shift();
    }
    
    this.setState({ formHistory: history });
  }

  /**
   * Undo last change
   */
  undo() {
    if (this.state.formHistory.length > 0) {
      const history = [...this.state.formHistory];
      const previousState = history.pop();
      
      this.setState({
        formData: previousState.formData,
        formHistory: history
      });
      
      return true;
    }
    return false;
  }

  /**
   * Export current state for backup
   */
  exportState() {
    return {
      ...this.state,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * Import state from backup
   */
  importState(importedState) {
    if (importedState && importedState.version === '1.0') {
      this.setState({
        selectedContractType: importedState.selectedContractType,
        formData: importedState.formData || {},
        currentStep: importedState.currentStep || 'selection'
      });
      return true;
    }
    return false;
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
    this.listeners = [];
  }
}

/**
 * Error handling utilities
 */
export class ErrorHandler {
  constructor() {
    this.errors = [];
    this.maxErrors = 50;
  }

  /**
   * Log an error
   */
  logError(error, context = {}) {
    const errorEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      message: error.message || error,
      stack: error.stack,
      context,
      type: this.categorizeError(error)
    };

    this.errors.unshift(errorEntry);
    
    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Legal Contract Generator Error:', errorEntry);
    }

    return errorEntry.id;
  }

  /**
   * Categorize error type
   */
  categorizeError(error) {
    if (error.name === 'ValidationError') return 'validation';
    if (error.message?.includes('network') || error.message?.includes('fetch')) return 'network';
    if (error.message?.includes('localStorage')) return 'storage';
    if (error.message?.includes('AI') || error.message?.includes('generation')) return 'ai';
    return 'general';
  }

  /**
   * Get errors by type
   */
  getErrorsByType(type) {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * Get recent errors
   */
  getRecentErrors(count = 10) {
    return this.errors.slice(0, count);
  }

  /**
   * Clear errors
   */
  clearErrors() {
    this.errors = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.filter(error => 
        Date.now() - new Date(error.timestamp).getTime() < 24 * 60 * 60 * 1000
      ).length
    };

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
    });

    return stats;
  }
}

/**
 * Form validation state manager
 */
export class ValidationStateManager {
  constructor() {
    this.validationRules = {};
    this.validationErrors = {};
    this.validationWarnings = {};
    this.isValidating = {};
  }

  /**
   * Set validation rules for a field
   */
  setValidationRules(fieldName, rules) {
    this.validationRules[fieldName] = rules;
  }

  /**
   * Validate a field
   */
  validateField(fieldName, value, context = {}) {
    const rules = this.validationRules[fieldName];
    if (!rules) return { isValid: true, errors: [], warnings: [] };

    const errors = [];
    const warnings = [];

    // Run validation rules
    rules.forEach(rule => {
      const result = rule.validate(value, context);
      if (!result.isValid) {
        if (result.severity === 'error') {
          errors.push(result.message);
        } else if (result.severity === 'warning') {
          warnings.push(result.message);
        }
      }
    });

    // Update state
    if (errors.length > 0) {
      this.validationErrors[fieldName] = errors[0]; // Show first error
    } else {
      delete this.validationErrors[fieldName];
    }

    if (warnings.length > 0) {
      this.validationWarnings[fieldName] = warnings;
    } else {
      delete this.validationWarnings[fieldName];
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Set validation state for a field
   */
  setValidationState(fieldName, isValidating) {
    if (isValidating) {
      this.isValidating[fieldName] = true;
    } else {
      delete this.isValidating[fieldName];
    }
  }

  /**
   * Get validation state
   */
  getValidationState() {
    return {
      errors: { ...this.validationErrors },
      warnings: { ...this.validationWarnings },
      isValidating: { ...this.isValidating }
    };
  }

  /**
   * Clear validation state
   */
  clearValidation(fieldName = null) {
    if (fieldName) {
      delete this.validationErrors[fieldName];
      delete this.validationWarnings[fieldName];
      delete this.isValidating[fieldName];
    } else {
      this.validationErrors = {};
      this.validationWarnings = {};
      this.isValidating = {};
    }
  }

  /**
   * Check if form is valid
   */
  isFormValid() {
    return Object.keys(this.validationErrors).length === 0;
  }
}

// Create singleton instances
export const contractStateManager = new ContractStateManager();
export const errorHandler = new ErrorHandler();
export const validationStateManager = new ValidationStateManager();
