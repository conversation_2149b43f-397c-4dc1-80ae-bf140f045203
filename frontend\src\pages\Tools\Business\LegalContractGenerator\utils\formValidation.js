// Form validation utilities for Legal Contract Generator

/**
 * Validate email format
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (basic validation)
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

/**
 * Validate required field
 */
const isRequired = (value) => {
  return value && value.toString().trim().length > 0;
};

/**
 * Validate minimum length
 */
const hasMinLength = (value, minLength) => {
  return value && value.toString().trim().length >= minLength;
};

/**
 * Validate maximum length
 */
const hasMaxLength = (value, maxLength) => {
  return !value || value.toString().trim().length <= maxLength;
};

/**
 * Validate numeric value
 */
const isNumeric = (value) => {
  return !isNaN(value) && !isNaN(parseFloat(value));
};

/**
 * Validate positive number
 */
const isPositiveNumber = (value) => {
  return isNumeric(value) && parseFloat(value) > 0;
};

/**
 * Common validation rules for all contract types
 */
const commonValidationRules = {
  // Basic Information
  partyOneName: { required: true, minLength: 2, maxLength: 100 },
  partyOneEmail: { required: true, email: true },
  partyOneAddress: { required: true, minLength: 10, maxLength: 200 },
  partyTwoName: { required: true, minLength: 2, maxLength: 100 },
  partyTwoEmail: { required: true, email: true },
  partyTwoAddress: { required: true, minLength: 10, maxLength: 200 },
  
  // Language and Jurisdiction
  language: { required: true },
  jurisdiction: { required: true },
  
  // Common terms
  effectiveDate: { required: true },
  contractTitle: { required: true, minLength: 5, maxLength: 100 }
};

/**
 * Contract-specific validation rules
 */
const contractSpecificRules = {
  service: {
    basic: {
      ...commonValidationRules
    },
    scope: {
      scopeOfWork: { required: true, minLength: 50, maxLength: 2000 },
      deliverables: { required: true, minLength: 20, maxLength: 1000 },
      timeline: { required: true, minLength: 10, maxLength: 500 }
    },
    terms: {
      paymentAmount: { required: true, numeric: true, positive: true },
      paymentSchedule: { required: true },
      paymentTerms: { required: true, minLength: 10, maxLength: 500 }
    },
    legal: {
      terminationClause: { required: true, minLength: 20, maxLength: 500 },
      intellectualProperty: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  partnership: {
    basic: {
      ...commonValidationRules,
      businessName: { required: true, minLength: 2, maxLength: 100 },
      businessType: { required: true },
      businessPurpose: { required: true, minLength: 20, maxLength: 500 }
    },
    financial: {
      capitalContribution: { required: true, numeric: true, positive: true },
      profitSharingRatio: { required: true, minLength: 10, maxLength: 200 },
      lossDistribution: { required: true, minLength: 10, maxLength: 200 }
    },
    management: {
      managementStructure: { required: true, minLength: 20, maxLength: 500 },
      decisionMaking: { required: true, minLength: 20, maxLength: 500 },
      rolesResponsibilities: { required: true, minLength: 20, maxLength: 1000 }
    },
    legal: {
      exitStrategy: { required: true, minLength: 20, maxLength: 500 },
      disputeResolution: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  nda: {
    basic: {
      ...commonValidationRules,
      disclosingParty: { required: true, minLength: 2, maxLength: 100 },
      receivingParty: { required: true, minLength: 2, maxLength: 100 },
      purpose: { required: true, minLength: 20, maxLength: 500 }
    },
    confidential: {
      confidentialInfo: { required: true, minLength: 50, maxLength: 1000 },
      exceptions: { required: true, minLength: 20, maxLength: 500 }
    },
    obligations: {
      obligations: { required: true, minLength: 50, maxLength: 1000 },
      restrictions: { required: true, minLength: 20, maxLength: 500 }
    },
    terms: {
      duration: { required: true, minLength: 5, maxLength: 100 },
      returnOfMaterials: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  freelance: {
    basic: {
      contractorName: { required: true, minLength: 2, maxLength: 100 },
      projectTitle: { required: true, minLength: 5, maxLength: 100 }
    },
    work: {
      workDescription: { required: true, minLength: 50, maxLength: 2000 },
      deliverables: { required: true, minLength: 20, maxLength: 1000 },
      timeline: { required: true, minLength: 10, maxLength: 500 }
    },
    payment: {
      paymentRate: { required: true, numeric: true, positive: true },
      paymentStructure: { required: true },
      paymentSchedule: { required: true, minLength: 10, maxLength: 500 }
    },
    legal: {
      intellectualProperty: { required: true, minLength: 20, maxLength: 500 },
      independentContractor: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  supplier: {
    basic: {
      supplierName: { required: true, minLength: 2, maxLength: 100 }
    },
    products: {
      productsServices: { required: true, minLength: 20, maxLength: 1000 },
      specifications: { required: true, minLength: 20, maxLength: 1000 },
      qualityStandards: { required: true, minLength: 20, maxLength: 500 }
    },
    terms: {
      pricing: { required: true, minLength: 20, maxLength: 500 },
      deliveryTerms: { required: true, minLength: 20, maxLength: 500 },
      paymentTerms: { required: true, minLength: 10, maxLength: 500 }
    },
    quality: {
      qualityAssurance: { required: true, minLength: 20, maxLength: 500 },
      warranties: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  employment: {
    basic: {
      employeeName: { required: true, minLength: 2, maxLength: 100 },
      jobTitle: { required: true, minLength: 2, maxLength: 100 }
    },
    compensation: {
      salary: { required: true, numeric: true, positive: true },
      payFrequency: { required: true },
      benefits: { required: true, minLength: 20, maxLength: 1000 }
    },
    duties: {
      jobDescription: { required: true, minLength: 50, maxLength: 2000 },
      workingHours: { required: true, minLength: 10, maxLength: 200 },
      workLocation: { required: true, minLength: 5, maxLength: 200 }
    },
    policies: {
      confidentiality: { required: true, minLength: 20, maxLength: 500 },
      terminationPolicy: { required: true, minLength: 20, maxLength: 500 }
    }
  },
  
  lease: {
    basic: {
      ...commonValidationRules,
      landlordName: { required: true, minLength: 2, maxLength: 100 },
      tenantName: { required: true, minLength: 2, maxLength: 100 },
      propertyAddress: { required: true, minLength: 10, maxLength: 200 },
      propertyType: { required: true }
    },
    financial: {
      monthlyRent: { required: true, numeric: true, positive: true },
      securityDeposit: { required: true, numeric: true, positive: true },
      leaseStartDate: { required: true },
      leaseDuration: { required: true, minLength: 5, maxLength: 100 }
    },
    conditions: {
      useRestrictions: { required: true, minLength: 20, maxLength: 500 },
      maintenanceResponsibilities: { required: true, minLength: 20, maxLength: 500 },
      petPolicy: { required: true, minLength: 10, maxLength: 200 }
    },
    legal: {
      terminationClause: { required: true, minLength: 20, maxLength: 500 },
      renewalOptions: { required: true, minLength: 10, maxLength: 300 }
    }
  }
};

/**
 * Validate a single field
 */
const validateField = (fieldName, value, rules) => {
  const errors = [];
  
  if (rules.required && !isRequired(value)) {
    errors.push('This field is required');
  }
  
  if (value && rules.email && !isValidEmail(value)) {
    errors.push('Please enter a valid email address');
  }
  
  if (value && rules.phone && !isValidPhone(value)) {
    errors.push('Please enter a valid phone number');
  }
  
  if (value && rules.minLength && !hasMinLength(value, rules.minLength)) {
    errors.push(`Minimum ${rules.minLength} characters required`);
  }
  
  if (value && rules.maxLength && !hasMaxLength(value, rules.maxLength)) {
    errors.push(`Maximum ${rules.maxLength} characters allowed`);
  }
  
  if (value && rules.numeric && !isNumeric(value)) {
    errors.push('Please enter a valid number');
  }
  
  if (value && rules.positive && !isPositiveNumber(value)) {
    errors.push('Please enter a positive number');
  }
  
  return errors;
};

/**
 * Validate a form step
 */
export const validateFormStep = (contractType, stepId, formData) => {
  const stepRules = contractSpecificRules[contractType]?.[stepId] || {};
  const errors = {};

  Object.keys(stepRules).forEach(fieldName => {
    const fieldValue = formData[fieldName];
    const fieldRules = stepRules[fieldName];
    const fieldErrors = validateField(fieldName, fieldValue, fieldRules);

    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors[0]; // Show only the first error
    }
  });

  return errors;
};

/**
 * Validate entire form
 */
export const validateEntireForm = (contractType, formData) => {
  const allStepRules = contractSpecificRules[contractType] || {};
  const errors = {};
  
  Object.values(allStepRules).forEach(stepRules => {
    Object.keys(stepRules).forEach(fieldName => {
      if (!errors[fieldName]) {
        const fieldErrors = validateField(fieldName, formData[fieldName], stepRules[fieldName]);
        if (fieldErrors.length > 0) {
          errors[fieldName] = fieldErrors[0];
        }
      }
    });
  });
  
  return errors;
};
