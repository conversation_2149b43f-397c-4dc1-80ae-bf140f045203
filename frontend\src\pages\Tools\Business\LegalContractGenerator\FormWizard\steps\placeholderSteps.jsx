// Placeholder step components for remaining contract types
import React, { useCallback } from 'react';
import { FormInput, FormTextarea, FormSelect, SectionHeader } from '../components/FormComponents';

// NDA Steps
export const NDAConfidentialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Confidential Information" description="What is protected" />

      <FormTextarea
        id="confidentialInfo"
        label="Confidential Information Definition"
        placeholder="Define what constitutes confidential information..."
        value={formData.confidentialInfo}
        onChange={handleChange}
        helpText="Clearly define what information is considered confidential"
        maxLength={1000}
        rows={6}
        error={validationErrors.confidentialInfo}
        required
      />

      <FormTextarea
        id="exceptions"
        label="Exceptions to Confidentiality"
        placeholder="List any exceptions to confidential treatment..."
        value={formData.exceptions}
        onChange={handleChange}
        helpText="Define information that is not considered confidential"
        maxLength={500}
        rows={4}
        error={validationErrors.exceptions}
        required
      />
    </div>
  );
};

export const NDAObligationsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Obligations" description="Duties and restrictions" />

      <FormTextarea
        id="obligations"
        label="Confidentiality Obligations"
        placeholder="Describe the obligations of the receiving party..."
        value={formData.obligations}
        onChange={handleChange}
        helpText="Define what the receiving party must do to protect confidential information"
        maxLength={1000}
        rows={6}
        error={validationErrors.obligations}
        required
      />

      <FormTextarea
        id="restrictions"
        label="Use Restrictions"
        placeholder="Define restrictions on how confidential information can be used..."
        value={formData.restrictions}
        onChange={handleChange}
        helpText="Specify limitations on the use of confidential information"
        maxLength={500}
        rows={4}
        error={validationErrors.restrictions}
        required
      />
    </div>
  );
};

export const NDATermsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Terms & Duration" description="Timeline and enforcement" />

      <FormInput
        id="duration"
        label="Confidentiality Duration"
        placeholder="e.g., 5 years, indefinite, until disclosed publicly"
        value={formData.duration}
        onChange={handleChange}
        helpText="How long the confidentiality obligations will last"
        maxLength={100}
        error={validationErrors.duration}
        required
      />

      <FormTextarea
        id="returnOfMaterials"
        label="Return of Materials"
        placeholder="Describe requirements for returning confidential materials..."
        value={formData.returnOfMaterials}
        onChange={handleChange}
        helpText="Define what happens to confidential materials when the agreement ends"
        maxLength={500}
        rows={4}
        error={validationErrors.returnOfMaterials}
        required
      />
    </div>
  );
};

// Freelance Steps
export const FreelanceBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Project Overview" description="Contractor and client details" />
      <FormInput
        id="contractorName"
        label="Contractor Name"
        value={formData.contractorName}
        onChange={handleChange}
        error={validationErrors.contractorName}
        required
      />
      <FormInput
        id="projectTitle"
        label="Project Title"
        value={formData.projectTitle}
        onChange={handleChange}
        error={validationErrors.projectTitle}
        required
      />
    </div>
  );
};

export const FreelanceWorkStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Work Description" description="Project scope and deliverables" />

      <FormTextarea
        id="workDescription"
        label="Work Description"
        placeholder="Describe the work to be performed in detail..."
        value={formData.workDescription}
        onChange={handleChange}
        helpText="Provide a comprehensive description of the work to be performed"
        maxLength={2000}
        rows={6}
        error={validationErrors.workDescription}
        required
      />

      <FormTextarea
        id="deliverables"
        label="Deliverables"
        placeholder="List all specific deliverables and outputs..."
        value={formData.deliverables}
        onChange={handleChange}
        helpText="Specify what will be delivered upon completion"
        maxLength={1000}
        rows={4}
        error={validationErrors.deliverables}
        required
      />

      <FormTextarea
        id="timeline"
        label="Project Timeline"
        placeholder="Outline the project schedule and milestones..."
        value={formData.timeline}
        onChange={handleChange}
        helpText="Include key milestones and completion dates"
        maxLength={500}
        rows={3}
        error={validationErrors.timeline}
        required
      />
    </div>
  );
};

export const FreelancePaymentStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Payment Terms" description="Rates and payment schedule" />

      <FormInput
        id="paymentRate"
        label="Payment Rate"
        type="number"
        placeholder="50"
        value={formData.paymentRate}
        onChange={handleChange}
        helpText="Hourly rate or total project amount"
        error={validationErrors.paymentRate}
        required
      />

      <FormSelect
        id="paymentStructure"
        label="Payment Structure"
        value={formData.paymentStructure}
        onChange={handleChange}
        helpText="How payments will be structured"
        error={validationErrors.paymentStructure}
        required
      >
        <option value="">Select Payment Structure</option>
        <option value="hourly">Hourly Rate</option>
        <option value="fixed">Fixed Project Fee</option>
        <option value="milestone">Milestone-Based</option>
        <option value="retainer">Monthly Retainer</option>
      </FormSelect>

      <FormTextarea
        id="paymentSchedule"
        label="Payment Schedule"
        placeholder="Describe when and how payments will be made..."
        value={formData.paymentSchedule}
        onChange={handleChange}
        helpText="Specify payment timing, methods, and terms"
        maxLength={500}
        rows={4}
        error={validationErrors.paymentSchedule}
        required
      />
    </div>
  );
};

export const FreelanceLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Rights and responsibilities" />

      <FormTextarea
        id="intellectualProperty"
        label="Intellectual Property Rights"
        placeholder="Define ownership of work products and materials..."
        value={formData.intellectualProperty}
        onChange={handleChange}
        helpText="Specify who owns the work product and any usage rights"
        maxLength={500}
        rows={4}
        error={validationErrors.intellectualProperty}
        required
      />

      <FormTextarea
        id="independentContractor"
        label="Independent Contractor Status"
        placeholder="Confirm independent contractor relationship and responsibilities..."
        value={formData.independentContractor}
        onChange={handleChange}
        helpText="Clarify the independent contractor relationship and obligations"
        maxLength={500}
        rows={4}
        error={validationErrors.independentContractor}
        required
      />
    </div>
  );
};

// Supplier Steps
export const SupplierBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Supplier Details" description="Parties and relationship" />
      <FormInput
        id="supplierName"
        label="Supplier Name"
        value={formData.supplierName}
        onChange={handleChange}
        error={validationErrors.supplierName}
        required
      />
    </div>
  );
};

export const SupplierProductsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Products/Services" description="What will be supplied" />

      <FormTextarea
        id="productsServices"
        label="Products/Services Description"
        placeholder="Describe the products or services to be supplied..."
        value={formData.productsServices}
        onChange={handleChange}
        helpText="Provide detailed description of what will be supplied"
        maxLength={1000}
        rows={5}
        error={validationErrors.productsServices}
        required
      />

      <FormTextarea
        id="specifications"
        label="Technical Specifications"
        placeholder="Detail the technical specifications and requirements..."
        value={formData.specifications}
        onChange={handleChange}
        helpText="Include technical details, dimensions, materials, etc."
        maxLength={1000}
        rows={5}
        error={validationErrors.specifications}
        required
      />

      <FormTextarea
        id="qualityStandards"
        label="Quality Standards"
        placeholder="Define the quality standards and requirements..."
        value={formData.qualityStandards}
        onChange={handleChange}
        helpText="Specify quality requirements, certifications, and standards"
        maxLength={500}
        rows={4}
        error={validationErrors.qualityStandards}
        required
      />
    </div>
  );
};

export const SupplierTermsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Commercial Terms" description="Pricing and delivery" />

      <FormTextarea
        id="pricing"
        label="Pricing Terms"
        placeholder="Detail the pricing structure, rates, and payment terms..."
        value={formData.pricing}
        onChange={handleChange}
        helpText="Include unit prices, volume discounts, and pricing conditions"
        maxLength={500}
        rows={4}
        error={validationErrors.pricing}
        required
      />

      <FormTextarea
        id="deliveryTerms"
        label="Delivery Terms"
        placeholder="Specify delivery schedules, locations, and conditions..."
        value={formData.deliveryTerms}
        onChange={handleChange}
        helpText="Include delivery timelines, shipping terms, and responsibilities"
        maxLength={500}
        rows={4}
        error={validationErrors.deliveryTerms}
        required
      />

      <FormTextarea
        id="paymentTerms"
        label="Payment Terms"
        placeholder="Define payment schedules, methods, and conditions..."
        value={formData.paymentTerms}
        onChange={handleChange}
        helpText="Specify payment timing, methods, and late payment terms"
        maxLength={500}
        rows={4}
        error={validationErrors.paymentTerms}
        required
      />
    </div>
  );
};

export const SupplierQualityStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Quality & Legal" description="Standards and compliance" />

      <FormTextarea
        id="qualityAssurance"
        label="Quality Assurance Procedures"
        placeholder="Describe quality control processes and procedures..."
        value={formData.qualityAssurance}
        onChange={handleChange}
        helpText="Detail quality control measures and testing procedures"
        maxLength={500}
        rows={4}
        error={validationErrors.qualityAssurance}
        required
      />

      <FormTextarea
        id="warranties"
        label="Warranties and Guarantees"
        placeholder="Define warranties, guarantees, and defect remedies..."
        value={formData.warranties}
        onChange={handleChange}
        helpText="Specify warranty periods, coverage, and remedy procedures"
        maxLength={500}
        rows={4}
        error={validationErrors.warranties}
        required
      />
    </div>
  );
};

// Employment Steps
export const EmploymentBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Employee Details" description="Position and employee info" />
      <FormInput
        id="employeeName"
        label="Employee Name"
        value={formData.employeeName}
        onChange={handleChange}
        error={validationErrors.employeeName}
        required
      />
      <FormInput
        id="jobTitle"
        label="Job Title"
        value={formData.jobTitle}
        onChange={handleChange}
        error={validationErrors.jobTitle}
        required
      />
    </div>
  );
};

export const EmploymentCompensationStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Compensation" description="Salary and benefits" />

      <FormInput
        id="salary"
        label="Annual Salary"
        type="number"
        placeholder="50000"
        value={formData.salary}
        onChange={handleChange}
        helpText="Annual salary amount in local currency"
        error={validationErrors.salary}
        required
      />

      <FormSelect
        id="payFrequency"
        label="Pay Frequency"
        value={formData.payFrequency}
        onChange={handleChange}
        helpText="How often the employee will be paid"
        error={validationErrors.payFrequency}
        required
      >
        <option value="">Select Pay Frequency</option>
        <option value="weekly">Weekly</option>
        <option value="bi-weekly">Bi-weekly</option>
        <option value="semi-monthly">Semi-monthly</option>
        <option value="monthly">Monthly</option>
      </FormSelect>

      <FormTextarea
        id="benefits"
        label="Benefits Package"
        placeholder="Describe the benefits offered to the employee..."
        value={formData.benefits}
        onChange={handleChange}
        helpText="Include health insurance, vacation, retirement, and other benefits"
        maxLength={1000}
        rows={6}
        error={validationErrors.benefits}
        required
      />
    </div>
  );
};

export const EmploymentDutiesStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Duties & Conditions" description="Job responsibilities" />

      <FormTextarea
        id="jobDescription"
        label="Job Description"
        placeholder="Describe the employee's duties and responsibilities..."
        value={formData.jobDescription}
        onChange={handleChange}
        helpText="Provide detailed description of job duties and expectations"
        maxLength={2000}
        rows={8}
        error={validationErrors.jobDescription}
        required
      />

      <FormTextarea
        id="workingHours"
        label="Working Hours"
        placeholder="Define the working hours and schedule..."
        value={formData.workingHours}
        onChange={handleChange}
        helpText="Specify work schedule, hours per week, and any flexibility"
        maxLength={200}
        rows={3}
        error={validationErrors.workingHours}
        required
      />

      <FormTextarea
        id="workLocation"
        label="Work Location"
        placeholder="Specify where the employee will work..."
        value={formData.workLocation}
        onChange={handleChange}
        helpText="Include office address, remote work options, or travel requirements"
        maxLength={200}
        rows={3}
        error={validationErrors.workLocation}
        required
      />
    </div>
  );
};

export const EmploymentPoliciesStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Policies & Terms" description="Company policies and legal terms" />

      <FormTextarea
        id="confidentiality"
        label="Confidentiality Terms"
        placeholder="Define confidentiality obligations and non-disclosure requirements..."
        value={formData.confidentiality}
        onChange={handleChange}
        helpText="Specify what information must be kept confidential"
        maxLength={500}
        rows={4}
        error={validationErrors.confidentiality}
        required
      />

      <FormTextarea
        id="terminationPolicy"
        label="Termination Policy"
        placeholder="Define termination procedures, notice periods, and severance..."
        value={formData.terminationPolicy}
        onChange={handleChange}
        helpText="Include notice requirements, severance terms, and termination procedures"
        maxLength={500}
        rows={4}
        error={validationErrors.terminationPolicy}
        required
      />
    </div>
  );
};

// Lease Steps
export const LeaseBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Property & Parties" description="Landlord, tenant, and property" />

      <FormInput
        id="landlordName"
        label="Landlord Name"
        placeholder="Full name of property owner"
        value={formData.landlordName}
        onChange={handleChange}
        helpText="Legal name of the property owner or landlord"
        maxLength={100}
        error={validationErrors.landlordName}
        required
      />

      <FormInput
        id="tenantName"
        label="Tenant Name"
        placeholder="Full name of tenant"
        value={formData.tenantName}
        onChange={handleChange}
        helpText="Legal name of the person or entity renting the property"
        maxLength={100}
        error={validationErrors.tenantName}
        required
      />

      <FormInput
        id="propertyAddress"
        label="Property Address"
        placeholder="Complete address of the rental property"
        value={formData.propertyAddress}
        onChange={handleChange}
        helpText="Full address including unit number, city, state, and zip code"
        maxLength={200}
        error={validationErrors.propertyAddress}
        required
      />

      <FormSelect
        id="propertyType"
        label="Property Type"
        value={formData.propertyType}
        onChange={handleChange}
        helpText="Type of property being leased"
        error={validationErrors.propertyType}
        required
      >
        <option value="">Select Property Type</option>
        <option value="residential">Residential</option>
        <option value="commercial">Commercial</option>
        <option value="industrial">Industrial</option>
        <option value="retail">Retail</option>
        <option value="office">Office</option>
      </FormSelect>
    </div>
  );
};

export const LeaseFinancialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Financial Terms" description="Rent and deposits" />

      <FormInput
        id="monthlyRent"
        label="Monthly Rent"
        type="number"
        placeholder="1500"
        value={formData.monthlyRent}
        onChange={handleChange}
        helpText="Monthly rental amount in local currency"
        error={validationErrors.monthlyRent}
        required
      />

      <FormInput
        id="securityDeposit"
        label="Security Deposit"
        type="number"
        placeholder="1500"
        value={formData.securityDeposit}
        onChange={handleChange}
        helpText="Security deposit amount required"
        error={validationErrors.securityDeposit}
        required
      />

      <FormInput
        id="leaseStartDate"
        label="Lease Start Date"
        type="date"
        value={formData.leaseStartDate}
        onChange={handleChange}
        helpText="When the lease term begins"
        error={validationErrors.leaseStartDate}
        required
      />

      <FormInput
        id="leaseDuration"
        label="Lease Duration"
        placeholder="e.g., 12 months, 2 years"
        value={formData.leaseDuration}
        onChange={handleChange}
        helpText="Length of the lease term"
        maxLength={100}
        error={validationErrors.leaseDuration}
        required
      />
    </div>
  );
};

export const LeaseConditionsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Lease Conditions" description="Rules and responsibilities" />

      <FormTextarea
        id="useRestrictions"
        label="Use Restrictions"
        placeholder="Define how the property can and cannot be used..."
        value={formData.useRestrictions}
        onChange={handleChange}
        helpText="Specify permitted uses and any restrictions on property use"
        maxLength={500}
        rows={4}
        error={validationErrors.useRestrictions}
        required
      />

      <FormTextarea
        id="maintenanceResponsibilities"
        label="Maintenance Responsibilities"
        placeholder="Define who is responsible for various maintenance tasks..."
        value={formData.maintenanceResponsibilities}
        onChange={handleChange}
        helpText="Specify landlord vs tenant maintenance obligations"
        maxLength={500}
        rows={4}
        error={validationErrors.maintenanceResponsibilities}
        required
      />

      <FormTextarea
        id="petPolicy"
        label="Pet Policy"
        placeholder="Define the policy regarding pets on the property..."
        value={formData.petPolicy}
        onChange={handleChange}
        helpText="Specify whether pets are allowed and any restrictions"
        maxLength={200}
        rows={3}
        error={validationErrors.petPolicy}
        required
      />
    </div>
  );
};

export const LeaseLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Termination and legal clauses" />

      <FormTextarea
        id="terminationClause"
        label="Termination Clause"
        placeholder="Define conditions and procedures for lease termination..."
        value={formData.terminationClause}
        onChange={handleChange}
        helpText="Specify notice requirements, early termination fees, and procedures"
        maxLength={500}
        rows={4}
        error={validationErrors.terminationClause}
        required
      />

      <FormTextarea
        id="renewalOptions"
        label="Renewal Options"
        placeholder="Define options for lease renewal..."
        value={formData.renewalOptions}
        onChange={handleChange}
        helpText="Specify renewal terms, notice requirements, and rent adjustments"
        maxLength={300}
        rows={3}
        error={validationErrors.renewalOptions}
        required
      />
    </div>
  );
};
