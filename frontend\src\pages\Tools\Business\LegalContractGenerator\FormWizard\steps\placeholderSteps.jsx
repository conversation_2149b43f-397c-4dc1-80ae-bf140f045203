// Placeholder step components for remaining contract types
import React, { useCallback } from 'react';
import { FormInput, FormTextarea, FormSelect, SectionHeader } from '../components/FormComponents';

// NDA Steps
export const NDAConfidentialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Confidential Information" description="What is protected" />
      <FormTextarea
        id="confidentialInfo"
        label="Confidential Information Definition"
        value={formData.confidentialInfo}
        onChange={handleChange}
        error={validationErrors.confidentialInfo}
        required
      />
    </div>
  );
};

export const NDAObligationsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Obligations" description="Duties and restrictions" />
      <FormTextarea
        id="obligations"
        label="Confidentiality Obligations"
        value={formData.obligations}
        onChange={handleChange}
        error={validationErrors.obligations}
        required
      />
    </div>
  );
};

export const NDATermsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Terms & Duration" description="Timeline and enforcement" />
      <FormInput
        id="duration"
        label="Confidentiality Duration"
        value={formData.duration}
        onChange={handleChange}
        error={validationErrors.duration}
        required
      />
    </div>
  );
};

// Freelance Steps
export const FreelanceBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Project Overview" description="Contractor and client details" />
      <FormInput
        id="contractorName"
        label="Contractor Name"
        value={formData.contractorName}
        onChange={handleChange}
        error={validationErrors.contractorName}
        required
      />
      <FormInput
        id="projectTitle"
        label="Project Title"
        value={formData.projectTitle}
        onChange={handleChange}
        error={validationErrors.projectTitle}
        required
      />
    </div>
  );
};

export const FreelanceWorkStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Work Description" description="Project scope and deliverables" />
      <FormTextarea
        id="workDescription"
        label="Work Description"
        value={formData.workDescription}
        onChange={handleChange}
        error={validationErrors.workDescription}
        required
      />
    </div>
  );
};

export const FreelancePaymentStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Payment Terms" description="Rates and payment schedule" />
      <FormInput
        id="paymentRate"
        label="Payment Rate"
        type="number"
        value={formData.paymentRate}
        onChange={handleChange}
        error={validationErrors.paymentRate}
        required
      />
    </div>
  );
};

export const FreelanceLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Rights and responsibilities" />
      <FormTextarea
        id="intellectualProperty"
        label="Intellectual Property"
        value={formData.intellectualProperty}
        onChange={handleChange}
        error={validationErrors.intellectualProperty}
        required
      />
    </div>
  );
};

// Supplier Steps
export const SupplierBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Supplier Details" description="Parties and relationship" />
      <FormInput
        id="supplierName"
        label="Supplier Name"
        value={formData.supplierName}
        onChange={handleChange}
        error={validationErrors.supplierName}
        required
      />
    </div>
  );
};

export const SupplierProductsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Products/Services" description="What will be supplied" />
      <FormTextarea
        id="productsServices"
        label="Products/Services"
        value={formData.productsServices}
        onChange={handleChange}
        error={validationErrors.productsServices}
        required
      />
    </div>
  );
};

export const SupplierTermsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Commercial Terms" description="Pricing and delivery" />
      <FormTextarea
        id="pricing"
        label="Pricing Terms"
        value={formData.pricing}
        onChange={handleChange}
        error={validationErrors.pricing}
        required
      />
    </div>
  );
};

export const SupplierQualityStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Quality & Legal" description="Standards and compliance" />
      <FormTextarea
        id="qualityAssurance"
        label="Quality Assurance"
        value={formData.qualityAssurance}
        onChange={handleChange}
        error={validationErrors.qualityAssurance}
        required
      />
    </div>
  );
};

// Employment Steps
export const EmploymentBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Employee Details" description="Position and employee info" />
      <FormInput
        id="employeeName"
        label="Employee Name"
        value={formData.employeeName}
        onChange={handleChange}
        error={validationErrors.employeeName}
        required
      />
      <FormInput
        id="jobTitle"
        label="Job Title"
        value={formData.jobTitle}
        onChange={handleChange}
        error={validationErrors.jobTitle}
        required
      />
    </div>
  );
};

export const EmploymentCompensationStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Compensation" description="Salary and benefits" />
      <FormInput
        id="salary"
        label="Annual Salary"
        type="number"
        value={formData.salary}
        onChange={handleChange}
        error={validationErrors.salary}
        required
      />
    </div>
  );
};

export const EmploymentDutiesStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Duties & Conditions" description="Job responsibilities" />
      <FormTextarea
        id="jobDescription"
        label="Job Description"
        value={formData.jobDescription}
        onChange={handleChange}
        error={validationErrors.jobDescription}
        required
      />
    </div>
  );
};

export const EmploymentPoliciesStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Policies & Terms" description="Company policies and legal terms" />
      <FormTextarea
        id="confidentiality"
        label="Confidentiality Terms"
        value={formData.confidentiality}
        onChange={handleChange}
        error={validationErrors.confidentiality}
        required
      />
    </div>
  );
};

// Lease Steps
export const LeaseBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Property & Parties" description="Landlord, tenant, and property" />
      <FormInput
        id="landlordName"
        label="Landlord Name"
        value={formData.landlordName}
        onChange={handleChange}
        error={validationErrors.landlordName}
        required
      />
      <FormInput
        id="propertyAddress"
        label="Property Address"
        value={formData.propertyAddress}
        onChange={handleChange}
        error={validationErrors.propertyAddress}
        required
      />
    </div>
  );
};

export const LeaseFinancialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Financial Terms" description="Rent and deposits" />
      <FormInput
        id="monthlyRent"
        label="Monthly Rent"
        type="number"
        value={formData.monthlyRent}
        onChange={handleChange}
        error={validationErrors.monthlyRent}
        required
      />
    </div>
  );
};

export const LeaseConditionsStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Lease Conditions" description="Rules and responsibilities" />
      <FormTextarea
        id="useRestrictions"
        label="Use Restrictions"
        value={formData.useRestrictions}
        onChange={handleChange}
        error={validationErrors.useRestrictions}
        required
      />
    </div>
  );
};

export const LeaseLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Termination and legal clauses" />
      <FormTextarea
        id="terminationClause"
        label="Termination Clause"
        value={formData.terminationClause}
        onChange={handleChange}
        error={validationErrors.terminationClause}
        required
      />
    </div>
  );
};
