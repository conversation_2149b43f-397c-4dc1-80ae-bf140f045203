// AI Integration utilities for Legal Contract Generator

/**
 * Get AI-powered clause recommendations based on contract type and context
 */
export const getClauseRecommendations = async (contractType, formData, token) => {
  try {
    const response = await fetch('/api/legal-contracts/clause-recommendations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        contractType,
        formData,
        context: 'clause_recommendations'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get clause recommendations');
    }

    const data = await response.json();
    return data.recommendations || [];
  } catch (error) {
    console.error('Error getting clause recommendations:', error);
    return [];
  }
};

/**
 * Get legal compliance suggestions for specific jurisdiction
 */
export const getComplianceSuggestions = async (contractType, jurisdiction, formData, token) => {
  try {
    const response = await fetch('/api/legal-contracts/compliance-check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        contractType,
        jurisdiction,
        formData,
        context: 'compliance_check'
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get compliance suggestions');
    }

    const data = await response.json();
    return data.suggestions || [];
  } catch (error) {
    console.error('Error getting compliance suggestions:', error);
    return [];
  }
};

/**
 * Validate field content using AI
 */
export const validateFieldWithAI = async (fieldName, value, contractType, context, token) => {
  try {
    const response = await fetch('/api/legal-contracts/field-validation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        fieldName,
        value,
        contractType,
        context
      })
    });

    if (!response.ok) {
      throw new Error('Failed to validate field');
    }

    const data = await response.json();
    return {
      isValid: data.isValid,
      suggestions: data.suggestions || [],
      warnings: data.warnings || []
    };
  } catch (error) {
    console.error('Error validating field with AI:', error);
    return {
      isValid: true,
      suggestions: [],
      warnings: []
    };
  }
};

/**
 * Get smart suggestions for form fields based on context
 */
export const getSmartSuggestions = (contractType, fieldName, currentValue, formData) => {
  const suggestions = {
    service: {
      scopeOfWork: [
        "Design and development of responsive website with modern UI/UX",
        "Digital marketing strategy including SEO, social media, and content marketing",
        "Business consulting services for operational efficiency and growth strategy",
        "Software development including requirements analysis, coding, testing, and deployment"
      ],
      deliverables: [
        "Complete website with source code, documentation, and training materials",
        "Marketing strategy document, campaign materials, and performance reports",
        "Business analysis report with recommendations and implementation roadmap",
        "Fully functional software application with user manual and technical documentation"
      ],
      paymentSchedule: [
        "50% upfront, 50% upon completion",
        "30% upfront, 40% at milestone, 30% upon completion",
        "25% upfront, 25% at design approval, 25% at development completion, 25% final delivery",
        "Monthly payments over project duration"
      ]
    },
    partnership: {
      businessPurpose: [
        "To operate a digital marketing agency providing comprehensive online marketing solutions",
        "To develop and market innovative software solutions for small and medium businesses",
        "To provide professional consulting services in business strategy and operations",
        "To create and distribute educational content and training programs"
      ],
      profitSharingRatio: [
        "Equal 50/50 split of all profits and losses",
        "60/40 split based on capital contribution ratio",
        "Performance-based sharing with minimum guaranteed percentages",
        "Tiered sharing based on revenue milestones"
      ]
    },
    nda: {
      purpose: [
        "Evaluation of potential business partnership or joint venture opportunity",
        "Discussion of proprietary technology and intellectual property licensing",
        "Exploration of merger and acquisition possibilities",
        "Sharing of confidential business strategies and market research"
      ],
      confidentialInfo: [
        "All technical specifications, source code, algorithms, and proprietary methodologies",
        "Business plans, financial information, customer lists, and market strategies",
        "Product designs, prototypes, research data, and development roadmaps",
        "Trade secrets, know-how, and any non-public business information"
      ]
    },
    freelance: {
      workDescription: [
        "Design and develop a responsive e-commerce website with payment integration",
        "Create comprehensive brand identity including logo, style guide, and marketing materials",
        "Develop mobile application for iOS and Android with backend API integration",
        "Provide ongoing digital marketing services including content creation and social media management"
      ],
      paymentStructure: [
        "Fixed project fee paid in milestones",
        "Hourly rate with weekly invoicing",
        "Retainer-based monthly payments",
        "Performance-based compensation with bonuses"
      ]
    },
    supplier: {
      productsServices: [
        "High-quality office supplies including paper, stationery, and printing materials",
        "Professional IT equipment and software licensing solutions",
        "Catering services for corporate events and daily meal programs",
        "Maintenance and cleaning services for commercial facilities"
      ],
      qualityStandards: [
        "All products must meet ISO 9001 quality management standards",
        "Compliance with industry-specific regulations and safety requirements",
        "Regular quality audits and performance monitoring protocols",
        "Adherence to environmental sustainability and ethical sourcing standards"
      ]
    },
    employment: {
      jobDescription: [
        "Responsible for developing and implementing marketing strategies to drive business growth",
        "Lead software development projects from conception to deployment and maintenance",
        "Manage client relationships and provide exceptional customer service and support",
        "Oversee daily operations and ensure efficient workflow and quality standards"
      ],
      benefits: [
        "Health insurance, dental coverage, retirement plan, and paid time off",
        "Professional development opportunities, training budget, and conference attendance",
        "Flexible work arrangements, remote work options, and work-life balance support",
        "Performance bonuses, stock options, and career advancement opportunities"
      ]
    },
    lease: {
      useRestrictions: [
        "Property to be used solely for residential purposes, no commercial activities",
        "Office space for professional services only, no manufacturing or retail operations",
        "Retail space for specified business type with approved operating hours",
        "Mixed-use property with designated areas for residential and commercial activities"
      ],
      maintenanceResponsibilities: [
        "Landlord responsible for structural repairs, tenant responsible for routine maintenance",
        "Tenant responsible for all maintenance and repairs except major structural issues",
        "Shared responsibility with detailed breakdown of specific maintenance duties",
        "Professional property management company handles all maintenance coordination"
      ]
    }
  };

  const contractSuggestions = suggestions[contractType];
  if (!contractSuggestions || !contractSuggestions[fieldName]) {
    return [];
  }

  return contractSuggestions[fieldName];
};

/**
 * Get contextual help text for form fields
 */
export const getContextualHelp = (contractType, fieldName) => {
  const helpTexts = {
    service: {
      scopeOfWork: "Be specific about what services you'll provide. Include what IS and ISN'T included to prevent scope creep.",
      deliverables: "List exactly what the client will receive. Include formats, quantities, and specifications.",
      paymentAmount: "Consider your costs, time investment, and market rates when setting your price.",
      intellectualProperty: "Clearly define who owns the final work product and any pre-existing materials."
    },
    partnership: {
      businessPurpose: "Define the specific business activities and goals of your partnership.",
      capitalContribution: "Specify each partner's initial investment in money, property, or services.",
      profitSharingRatio: "Determine how profits and losses will be distributed among partners.",
      managementStructure: "Define roles, responsibilities, and decision-making authority."
    },
    nda: {
      purpose: "Explain why confidential information needs to be shared.",
      confidentialInfo: "Be specific about what information is considered confidential.",
      duration: "Consider how long the information needs protection - some may be indefinite.",
      obligations: "Define what the receiving party can and cannot do with the information."
    }
  };

  return helpTexts[contractType]?.[fieldName] || "Provide accurate and complete information for this field.";
};

/**
 * Detect potential issues in form data
 */
export const detectFormIssues = (contractType, formData) => {
  const issues = [];

  // Check for common issues across all contract types
  if (formData.partyOneName && formData.partyTwoName && 
      formData.partyOneName.toLowerCase() === formData.partyTwoName.toLowerCase()) {
    issues.push({
      type: 'warning',
      field: 'partyNames',
      message: 'Both parties have the same name. Please verify this is correct.'
    });
  }

  if (formData.effectiveDate) {
    const effectiveDate = new Date(formData.effectiveDate);
    const today = new Date();
    const daysDiff = (effectiveDate - today) / (1000 * 60 * 60 * 24);
    
    if (daysDiff < -30) {
      issues.push({
        type: 'warning',
        field: 'effectiveDate',
        message: 'Effective date is more than 30 days in the past. Consider using a current date.'
      });
    }
  }

  // Contract-specific issue detection
  if (contractType === 'service') {
    if (formData.paymentAmount && parseFloat(formData.paymentAmount) < 100) {
      issues.push({
        type: 'info',
        field: 'paymentAmount',
        message: 'Consider if this amount adequately covers your costs and time.'
      });
    }

    if (formData.scopeOfWork && formData.scopeOfWork.length < 50) {
      issues.push({
        type: 'warning',
        field: 'scopeOfWork',
        message: 'Consider providing more detail to prevent misunderstandings.'
      });
    }
  }

  if (contractType === 'partnership') {
    if (formData.capitalContribution && parseFloat(formData.capitalContribution) === 0) {
      issues.push({
        type: 'info',
        field: 'capitalContribution',
        message: 'Consider if any initial investment is needed for the partnership.'
      });
    }
  }

  return issues;
};

/**
 * Get jurisdiction-specific legal requirements
 */
export const getJurisdictionRequirements = (jurisdiction, contractType) => {
  const requirements = {
    'United States': {
      service: [
        'Include governing law clause specifying state jurisdiction',
        'Consider including dispute resolution mechanism',
        'Ensure compliance with applicable state contract laws'
      ],
      employment: [
        'Must comply with federal and state employment laws',
        'Include at-will employment clause where applicable',
        'Consider state-specific wage and hour requirements'
      ]
    },
    'United Kingdom': {
      service: [
        'Include jurisdiction clause for English courts',
        'Consider GDPR compliance for data processing',
        'Include proper contract formation elements'
      ],
      employment: [
        'Must comply with Employment Rights Act 1996',
        'Include statutory notice periods',
        'Consider TUPE regulations if applicable'
      ]
    },
    'European Union': {
      service: [
        'Ensure GDPR compliance for personal data',
        'Include appropriate jurisdiction clause',
        'Consider consumer protection regulations'
      ]
    }
  };

  return requirements[jurisdiction]?.[contractType] || [];
};
