import React, { useMemo } from 'react';
import { FiEye, FiFileText, FiCheck, FiAlertTriangle } from 'react-icons/fi';
import { CONTRACT_TYPES } from '../utils/contractConstants';
import { generatePreviewContent } from '../utils/contractTemplates';

const PreviewPane = ({ contractType, formData, currentStep }) => {
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  // Generate preview content based on current form data
  const previewContent = useMemo(() => {
    return generatePreviewContent(contractType, formData);
  }, [contractType, formData]);

  // Calculate completion percentage
  const completionPercentage = useMemo(() => {
    const requiredFields = getRequiredFieldsForContract(contractType);
    const completedFields = requiredFields.filter(field => 
      formData[field] && formData[field].toString().trim().length > 0
    );
    return Math.round((completedFields.length / requiredFields.length) * 100);
  }, [contractType, formData]);

  // Get completion status
  const getCompletionStatus = () => {
    if (completionPercentage >= 90) return { color: 'text-green-400', icon: FiCheck, label: 'Nearly Complete' };
    if (completionPercentage >= 60) return { color: 'text-yellow-400', icon: FiAlertTriangle, label: 'In Progress' };
    return { color: 'text-blue-400', icon: FiFileText, label: 'Getting Started' };
  };

  const status = getCompletionStatus();
  const StatusIcon = status.icon;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Preview Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700 bg-slate-800/50">
        <div className="flex items-center gap-3">
          <div className={`w-10 h-10 rounded-lg ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border flex items-center justify-center`}>
            <FiEye className={`w-5 h-5 ${contractTypeInfo.textColor}`} />
          </div>
          <div>
            <h3 className="text-white font-semibold">Live Preview</h3>
            <p className="text-slate-400 text-sm">{contractTypeInfo.title}</p>
          </div>
        </div>
        
        {/* Completion Status */}
        <div className="flex items-center gap-2">
          <StatusIcon className={`w-4 h-4 ${status.color}`} />
          <span className={`text-sm font-medium ${status.color}`}>
            {status.label}
          </span>
          <span className="text-slate-400 text-sm">
            ({completionPercentage}%)
          </span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="px-4 py-2 bg-slate-800/30">
        <div className="w-full bg-slate-700 rounded-full h-1.5">
          <div
            className={`h-1.5 rounded-full transition-all duration-500 ${
              completionPercentage >= 90 
                ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                : completionPercentage >= 60
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                  : 'bg-gradient-to-r from-blue-500 to-purple-500'
            }`}
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {previewContent ? (
            <div className="bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
              {/* Document Header */}
              <div className="bg-slate-50 border-b border-slate-200 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-lg font-bold text-slate-800">
                      {formData.contractTitle || `${contractTypeInfo.title} - Draft`}
                    </h4>
                    <p className="text-slate-600 text-sm">
                      {formData.effectiveDate ? `Effective: ${formData.effectiveDate}` : 'Effective Date: [To be specified]'}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-slate-600 text-sm">
                      {formData.jurisdiction || '[Jurisdiction]'}
                    </p>
                    <p className="text-slate-500 text-xs">
                      Draft Preview
                    </p>
                  </div>
                </div>
              </div>

              {/* Document Content */}
              <div 
                className="p-6 text-slate-800 leading-relaxed"
                style={{ 
                  fontFamily: 'Georgia, serif',
                  fontSize: '14px',
                  lineHeight: '1.7'
                }}
              >
                <div 
                  className="contract-preview"
                  dangerouslySetInnerHTML={{ __html: previewContent }}
                />
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiFileText className="w-8 h-8 text-slate-400" />
              </div>
              <h4 className="text-slate-300 font-semibold mb-2">Preview Will Appear Here</h4>
              <p className="text-slate-500 text-sm max-w-sm mx-auto">
                Start filling out the form to see a live preview of your contract. 
                The preview updates automatically as you enter information.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Preview Footer */}
      <div className="p-4 border-t border-slate-700 bg-slate-800/30">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2 text-slate-400">
            <FiFileText className="w-4 h-4" />
            <span>Live Preview</span>
          </div>
          <div className="text-slate-500">
            Updates automatically
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to get required fields for a contract type
const getRequiredFieldsForContract = (contractType) => {
  const commonFields = [
    'contractTitle', 'effectiveDate', 'partyOneName', 'partyOneEmail', 
    'partyOneAddress', 'partyTwoName', 'partyTwoEmail', 'partyTwoAddress',
    'language', 'jurisdiction'
  ];

  const specificFields = {
    service: [...commonFields, 'scopeOfWork', 'deliverables', 'timeline', 'paymentAmount', 'paymentSchedule', 'paymentTerms', 'terminationClause', 'intellectualProperty'],
    partnership: [...commonFields, 'businessName', 'businessType', 'businessPurpose', 'capitalContribution', 'profitSharingRatio', 'managementStructure', 'exitStrategy'],
    nda: [...commonFields, 'disclosingParty', 'receivingParty', 'purpose', 'confidentialInfo', 'obligations', 'duration'],
    freelance: [...commonFields, 'contractorName', 'clientName', 'projectTitle', 'workDescription', 'deliverables', 'paymentRate', 'paymentStructure', 'intellectualProperty'],
    supplier: [...commonFields, 'supplierName', 'buyerName', 'productsServices', 'specifications', 'pricing', 'deliveryTerms', 'qualityAssurance'],
    employment: [...commonFields, 'employeeName', 'employerName', 'jobTitle', 'startDate', 'salary', 'payFrequency', 'jobDescription', 'confidentiality'],
    lease: [...commonFields, 'landlordName', 'tenantName', 'propertyAddress', 'propertyType', 'monthlyRent', 'securityDeposit', 'leaseStartDate', 'useRestrictions']
  };

  return specificFields[contractType] || commonFields;
};

export default PreviewPane;
