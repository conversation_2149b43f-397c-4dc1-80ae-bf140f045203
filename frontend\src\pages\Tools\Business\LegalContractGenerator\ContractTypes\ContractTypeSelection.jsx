import React, { useState } from 'react';
import { FiArrowR<PERSON>, FiCheck } from 'react-icons/fi';
import ContractTypeCard from './ContractTypeCard';

const ContractTypeSelection = ({ onSelectType, contractTypes }) => {
  const [selectedType, setSelectedType] = useState(null);
  const [hoveredType, setHoveredType] = useState(null);

  const handleTypeSelect = (typeId) => {
    setSelectedType(typeId);
  };

  const handleContinue = () => {
    if (selectedType && contractTypes[selectedType]) {
      onSelectType(selectedType);
    }
  };

  const contractTypesArray = Object.values(contractTypes);

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="inline-flex items-center justify-center p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full mb-6 backdrop-blur-sm border border-white/10">
          <span className="text-blue-400 font-semibold text-lg">Choose Contract Type</span>
        </div>
        <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-4">
          Select Your Contract
        </h2>
        <p className="text-slate-400 text-lg max-w-3xl mx-auto leading-relaxed">
          Choose the type of legal contract you need to generate. Each template is professionally crafted 
          with industry-standard clauses and AI-powered customization.
        </p>
      </div>

      {/* Contract Type Grid */}
      <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        {contractTypesArray.map((contractType, index) => (
          <ContractTypeCard
            key={contractType.id}
            contractType={contractType}
            isSelected={selectedType === contractType.id}
            isHovered={hoveredType === contractType.id}
            onSelect={handleTypeSelect}
            onHover={setHoveredType}
            delay={index * 100}
          />
        ))}
      </div>

      {/* Continue Button */}
      {selectedType && (
        <div className="flex justify-center animate-fade-in">
          <button
            onClick={handleContinue}
            className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 rounded-xl font-semibold text-white transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25 flex items-center gap-3"
          >
            <FiCheck className="w-5 h-5" />
            <span>Continue with {contractTypes[selectedType]?.title}</span>
            <FiArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
          </button>
        </div>
      )}

      {/* Legal Disclaimer */}
      <div className="mt-12 p-6 bg-slate-900/50 border border-slate-700 rounded-xl">
        <div className="flex items-start gap-3">
          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
          <div>
            <h4 className="text-yellow-400 font-semibold mb-2">Legal Disclaimer</h4>
            <p className="text-slate-400 text-sm leading-relaxed">
              The contracts generated by this tool are templates designed to provide a starting point for legal agreements. 
              While our AI incorporates industry best practices and legal standards, we strongly recommend having any contract 
              reviewed by a qualified attorney before execution. This tool does not constitute legal advice, and Dosky is not 
              responsible for the legal validity or enforceability of generated contracts.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractTypeSelection;
