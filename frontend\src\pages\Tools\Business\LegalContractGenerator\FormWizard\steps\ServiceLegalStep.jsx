import React, { useCallback } from 'react';
import { FiShield, FiInfo , FiXCircle } from 'react-icons/fi';
import { FormTextarea, FormCheckbox, SectionHeader } from '../components/FormComponents';

const ServiceLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    onFormDataChange({ [name]: type === 'checkbox' ? checked : value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Intellectual Property */}
      <div>
        <SectionHeader 
          title="Intellectual Property Rights" 
          description="Ownership of work products and materials"
          icon={FiInfo}
        />
        <FormTextarea
          id="intellectualProperty"
          label="Intellectual Property Terms"
          placeholder="Define who owns the work product, any existing IP, and usage rights..."
          value={formData.intellectualProperty}
          onChange={handleChange}
          helpText="Specify ownership of deliverables, existing IP, and usage rights"
          tooltip="Clearly define who owns the final work product, any pre-existing intellectual property, and how each party can use the materials."
          maxLength={500}
          rows={5}
          error={validationErrors.intellectualProperty}
          required
          examples="'Client owns all final deliverables upon full payment. Service provider retains rights to general methodologies and pre-existing IP. Client grants permission to use project as portfolio example.'"
        />
      </div>

      {/* Termination Clause */}
      <div>
        <SectionHeader 
          title="Termination Terms" 
          description="Conditions for ending the contract"
          icon={FiXCircle}
        />
        <FormTextarea
          id="terminationClause"
          label="Termination Conditions"
          placeholder="Describe how either party can terminate the contract, notice periods, and consequences..."
          value={formData.terminationClause}
          onChange={handleChange}
          helpText="Define termination conditions, notice requirements, and payment obligations"
          tooltip="Include notice periods, reasons for termination, and what happens to work completed and payments made."
          maxLength={500}
          rows={5}
          error={validationErrors.terminationClause}
          required
          examples="'Either party may terminate with 14 days written notice. Client pays for work completed to date. Service provider delivers all work in progress. Termination for cause requires no notice.'"
        />
      </div>

      {/* Liability and Warranties */}
      <div>
        <SectionHeader 
          title="Liability & Warranties" 
          description="Risk allocation and service guarantees"
          icon={FiShield}
        />
        <div className="space-y-6">
          <FormTextarea
            id="warranties"
            label="Warranties and Guarantees (Optional)"
            placeholder="Describe any warranties or guarantees provided with the services..."
            value={formData.warranties}
            onChange={handleChange}
            helpText="What guarantees do you provide for your work?"
            tooltip="Include any warranties on the quality of work, bug fixes, or performance guarantees."
            maxLength={300}
            rows={3}
            examples="'Service provider warrants work will be free from defects for 30 days. Bug fixes provided at no charge during warranty period.'"
          />
          
          <FormTextarea
            id="liabilityLimitation"
            label="Liability Limitation (Optional)"
            placeholder="Define limitations on liability and damages..."
            value={formData.liabilityLimitation}
            onChange={handleChange}
            helpText="How will liability be limited between the parties?"
            tooltip="Specify limits on liability, types of damages excluded, and maximum liability amounts."
            maxLength={300}
            rows={3}
            examples="'Total liability limited to contract value. No liability for indirect, consequential, or lost profit damages. Client responsible for data backup.'"
          />
        </div>
      </div>

      {/* Additional Legal Terms */}
      <div>
        <SectionHeader 
          title="Additional Legal Terms" 
          description="Other important legal considerations"
          icon={FiShield}
        />
        <div className="space-y-6">
          <FormCheckbox
            id="includeConfidentiality"
            label="Include Confidentiality Clause"
            checked={formData.includeConfidentiality}
            onChange={handleChange}
            helpText="Add mutual confidentiality obligations"
            tooltip="This will include standard confidentiality terms to protect sensitive information shared during the project."
          />
          
          <FormCheckbox
            id="includeIndemnification"
            label="Include Indemnification Clause"
            checked={formData.includeIndemnification}
            onChange={handleChange}
            helpText="Add mutual indemnification protection"
            tooltip="This protects each party from claims arising from the other party's actions or negligence."
          />
          
          <FormCheckbox
            id="includeForcemajeure"
            label="Include Force Majeure Clause"
            checked={formData.includeForcemajeure}
            onChange={handleChange}
            helpText="Protection against unforeseeable circumstances"
            tooltip="This protects both parties from liability when performance is prevented by extraordinary circumstances beyond their control."
          />
          
          <FormTextarea
            id="additionalTerms"
            label="Additional Terms (Optional)"
            placeholder="Any other specific terms or conditions for this contract..."
            value={formData.additionalTerms}
            onChange={handleChange}
            helpText="Include any other important terms specific to this project"
            tooltip="Add any project-specific terms, special conditions, or requirements not covered above."
            maxLength={500}
            rows={4}
            examples="'All communications must be in writing. Project requires security clearance. Work must comply with GDPR requirements.'"
          />
        </div>
      </div>

      {/* Legal Guidelines */}
      <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-6">
        <h4 className="text-red-400 font-semibold mb-3 flex items-center gap-2">
          <FiShield className="w-4 h-4" />
          Legal Protection Guidelines
        </h4>
        <ul className="text-slate-300 text-sm space-y-2">
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Always include clear IP ownership terms to avoid disputes</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Limit your liability to protect against excessive claims</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Include termination terms that protect your interests</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Consider professional liability insurance for additional protection</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ServiceLegalStep;
