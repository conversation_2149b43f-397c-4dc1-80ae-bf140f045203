/* Legal Contract Generator Styles */

/* Animation keyframes */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* Utility classes */
.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out forwards;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Legal industry color palette */
.legal-primary {
  color: #1e40af; /* Professional blue */
}

.legal-secondary {
  color: #374151; /* Dark gray */
}

.legal-accent {
  color: #059669; /* Success green */
}

.legal-warning {
  color: #d97706; /* Warning amber */
}

.legal-danger {
  color: #dc2626; /* Error red */
}

/* Professional typography */
.legal-heading {
  font-family: 'Georgia', 'Times New Roman', serif;
  font-weight: 600;
  line-height: 1.2;
  color: #1f2937;
}

.legal-body {
  font-family: 'Inter', 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: #374151;
}

.legal-document {
  font-family: 'Georgia', 'Times New Roman', serif;
  line-height: 1.8;
  color: #1f2937;
}

/* Contract type cards */
.contract-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
  backdrop-filter: blur(10px);
}

.contract-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.contract-card.selected {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.1) 100%);
  border: 2px solid rgba(59, 130, 246, 0.5);
}

/* Form styling */
.legal-form-input {
  background: rgba(51, 65, 85, 0.5);
  border: 1px solid rgba(71, 85, 105, 0.6);
  border-radius: 12px;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.legal-form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(51, 65, 85, 0.7);
}

.legal-form-input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.legal-form-input.success {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

/* Step indicator */
.step-indicator {
  position: relative;
}

.step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #374151 0%, #6b7280 100%);
  z-index: 1;
}

.step-circle {
  position: relative;
  z-index: 2;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.step-circle.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.step-circle.current {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  animation: pulse-glow 2s ease-in-out infinite;
}

.step-circle.pending {
  background: rgba(55, 65, 81, 0.8);
  border: 2px solid #4b5563;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots > div {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #3b82f6;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots > div:nth-child(1) { animation-delay: -0.32s; }
.loading-dots > div:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Contract preview */
.contract-preview {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.contract-preview-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 20px;
}

.contract-preview-content {
  padding: 30px;
  font-family: 'Georgia', 'Times New Roman', serif;
  line-height: 1.8;
  color: #1f2937;
}

/* Responsive design */
@media (max-width: 768px) {
  .contract-card {
    margin-bottom: 16px;
  }
  
  .step-indicator {
    flex-direction: column;
    gap: 16px;
  }
  
  .step-indicator::before {
    display: none;
  }
  
  .contract-preview-content {
    padding: 20px;
    font-size: 14px;
  }
}

@media (max-width: 640px) {
  .legal-form-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .contract-card {
    padding: 16px;
  }
  
  .step-circle {
    width: 40px;
    height: 40px;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .contract-preview {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .contract-preview-header {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    border-bottom-color: #4b5563;
  }
  
  .contract-preview-content {
    color: #f9fafb;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .contract-preview {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .contract-preview-content {
    color: #000;
    font-size: 12pt;
    line-height: 1.6;
  }
  
  .page-break {
    page-break-before: always;
  }
  
  .avoid-break {
    page-break-inside: avoid;
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .contract-card {
    border: 2px solid;
  }
  
  .legal-form-input {
    border-width: 2px;
  }
  
  .step-circle {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .animate-pulse-glow {
    animation: none;
  }
}
