import React, { useCallback } from 'react';
import { FiBriefcase, FiTarget } from 'react-icons/fi';
import { FormInput, FormSelect, FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipBasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Business Information */}
      <div>
        <SectionHeader 
          title="Business Information" 
          description="Details about the partnership business"
          icon={FiBriefcase}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="businessName"
            label="Business/Partnership Name"
            placeholder="ABC Partnership LLC"
            value={formData.businessName}
            onChange={handleChange}
            helpText="The legal name of the partnership entity"
            tooltip="This will be the official name under which the partnership operates."
            maxLength={100}
            error={validationErrors.businessName}
            required
          />
          <FormSelect
            id="businessType"
            label="Partnership Type"
            value={formData.businessType}
            onChange={handleChange}
            helpText="Legal structure of the partnership"
            tooltip="Choose the appropriate legal structure for your partnership."
            error={validationErrors.businessType}
            required
          >
            <option value="">Select Partnership Type</option>
            <option value="general">General Partnership</option>
            <option value="limited">Limited Partnership</option>
            <option value="llp">Limited Liability Partnership (LLP)</option>
            <option value="llc">Limited Liability Company (LLC)</option>
            <option value="joint-venture">Joint Venture</option>
          </FormSelect>
        </div>
      </div>

      {/* Business Purpose */}
      <div>
        <SectionHeader 
          title="Business Purpose" 
          description="The purpose and scope of the partnership"
          icon={FiTarget}
        />
        <FormTextarea
          id="businessPurpose"
          label="Partnership Purpose & Scope"
          placeholder="Describe the business purpose, activities, and scope of the partnership..."
          value={formData.businessPurpose}
          onChange={handleChange}
          helpText="Define what the partnership will do and its business objectives"
          tooltip="Be specific about the business activities, target market, and goals of the partnership."
          maxLength={500}
          rows={5}
          error={validationErrors.businessPurpose}
          required
          examples="'To operate a digital marketing agency providing SEO, social media, and content marketing services to small and medium businesses in the technology sector.'"
        />
      </div>

      {/* Partnership Duration */}
      <div className="grid md:grid-cols-2 gap-6">
        <FormSelect
          id="partnershipDuration"
          label="Partnership Duration"
          value={formData.partnershipDuration}
          onChange={handleChange}
          helpText="How long will this partnership last?"
          tooltip="Specify whether this is a permanent partnership or has a specific end date."
        >
          <option value="">Select Duration</option>
          <option value="indefinite">Indefinite (until dissolved)</option>
          <option value="fixed">Fixed Term</option>
          <option value="project">Project-Based</option>
        </FormSelect>
        
        {formData.partnershipDuration === 'fixed' && (
          <FormInput
            id="partnershipEndDate"
            label="End Date"
            type="date"
            value={formData.partnershipEndDate}
            onChange={handleChange}
            helpText="When will the partnership end?"
            tooltip="Specify the exact date when the partnership will terminate."
          />
        )}
      </div>
    </div>
  );
};

export default PartnershipBasicStep;
