import React, { useCallback } from 'react';
import { FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Exit strategies and dispute resolution" />
      <FormTextarea
        id="exitStrategy"
        label="Exit Strategy"
        value={formData.exitStrategy}
        onChange={handleChange}
        error={validationErrors.exitStrategy}
        required
      />
    </div>
  );
};

export default PartnershipLegalStep;
