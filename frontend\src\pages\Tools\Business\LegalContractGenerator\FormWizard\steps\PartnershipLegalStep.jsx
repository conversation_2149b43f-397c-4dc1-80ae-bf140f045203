import React, { useCallback } from 'react';
import { FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipLegalStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Legal Terms" description="Exit strategies and dispute resolution" />

      <FormTextarea
        id="exitStrategy"
        label="Exit Strategy"
        placeholder="Describe how partners can exit the partnership..."
        value={formData.exitStrategy}
        onChange={handleChange}
        helpText="Define procedures for partner withdrawal, buyout terms, and asset distribution"
        maxLength={500}
        rows={4}
        error={validationErrors.exitStrategy}
        required
      />

      <FormTextarea
        id="disputeResolution"
        label="Dispute Resolution"
        placeholder="Describe how disputes between partners will be resolved..."
        value={formData.disputeResolution}
        onChange={handleChange}
        helpText="Define mediation, arbitration, or other dispute resolution procedures"
        maxLength={500}
        rows={4}
        error={validationErrors.disputeResolution}
        required
      />
    </div>
  );
};

export default PartnershipLegalStep;
