import React from 'react';
import { FiInfo, FiAlertTriangle } from 'react-icons/fi';

// Tooltip component
export const Tooltip = ({ children, content }) => (
  <div className="group relative inline-block">
    {children}
    <div className="invisible group-hover:visible absolute z-10 w-64 p-3 mt-2 text-sm text-white bg-slate-800 border border-slate-600 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 left-0">
      {content}
    </div>
  </div>
);

// Form Input component
export const FormInput = ({ 
  id, 
  label, 
  type = 'text',
  placeholder, 
  value, 
  onChange, 
  helpText, 
  maxLength, 
  tooltip, 
  examples,
  error,
  required = false,
  disabled = false
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-2">
      <label htmlFor={id} className="block text-sm font-medium text-slate-300">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
      {tooltip && (
        <Tooltip content={tooltip}>
          <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
        </Tooltip>
      )}
    </div>
    
    <input
      type={type}
      id={id}
      name={id}
      placeholder={placeholder}
      value={value || ''}
      onChange={onChange}
      maxLength={maxLength}
      disabled={disabled}
      className={`
        w-full px-4 py-3 bg-slate-700/50 border rounded-xl text-white placeholder-slate-400 
        transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
        ${error ? 'border-red-500 bg-red-500/10' : 'border-slate-600 hover:border-slate-500'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    />
    
    {error && (
      <div className="flex items-center gap-2 text-red-400 text-sm">
        <FiAlertTriangle className="w-4 h-4" />
        <span>{error}</span>
      </div>
    )}
    
    {helpText && !error && (
      <p className="text-slate-500 text-sm">{helpText}</p>
    )}
    
    {examples && !error && (
      <div className="text-slate-500 text-xs">
        <span className="font-medium">Examples:</span> {examples}
      </div>
    )}
    
    {maxLength && (
      <div className="text-right text-xs text-slate-500">
        {value ? value.length : 0}/{maxLength}
      </div>
    )}
  </div>
);

// Form Textarea component
export const FormTextarea = ({ 
  id, 
  label, 
  placeholder, 
  value, 
  onChange, 
  helpText, 
  maxLength, 
  tooltip, 
  examples,
  error,
  required = false,
  rows = 4,
  disabled = false
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-2">
      <label htmlFor={id} className="block text-sm font-medium text-slate-300">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
      {tooltip && (
        <Tooltip content={tooltip}>
          <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
        </Tooltip>
      )}
    </div>
    
    <textarea
      id={id}
      name={id}
      placeholder={placeholder}
      value={value || ''}
      onChange={onChange}
      maxLength={maxLength}
      rows={rows}
      disabled={disabled}
      className={`
        w-full px-4 py-3 bg-slate-700/50 border rounded-xl text-white placeholder-slate-400 
        transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
        resize-vertical min-h-[100px]
        ${error ? 'border-red-500 bg-red-500/10' : 'border-slate-600 hover:border-slate-500'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    />
    
    {error && (
      <div className="flex items-center gap-2 text-red-400 text-sm">
        <FiAlertTriangle className="w-4 h-4" />
        <span>{error}</span>
      </div>
    )}
    
    {helpText && !error && (
      <p className="text-slate-500 text-sm">{helpText}</p>
    )}
    
    {examples && !error && (
      <div className="text-slate-500 text-xs">
        <span className="font-medium">Examples:</span> {examples}
      </div>
    )}
    
    {maxLength && (
      <div className="text-right text-xs text-slate-500">
        {value ? value.length : 0}/{maxLength}
      </div>
    )}
  </div>
);

// Form Select component
export const FormSelect = ({ 
  id, 
  label, 
  value, 
  onChange, 
  helpText, 
  tooltip, 
  error,
  required = false,
  disabled = false,
  children
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-2">
      <label htmlFor={id} className="block text-sm font-medium text-slate-300">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
      {tooltip && (
        <Tooltip content={tooltip}>
          <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
        </Tooltip>
      )}
    </div>
    
    <select
      id={id}
      name={id}
      value={value || ''}
      onChange={onChange}
      disabled={disabled}
      className={`
        w-full px-4 py-3 bg-slate-700/50 border rounded-xl text-white 
        transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
        ${error ? 'border-red-500 bg-red-500/10' : 'border-slate-600 hover:border-slate-500'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    >
      {children}
    </select>
    
    {error && (
      <div className="flex items-center gap-2 text-red-400 text-sm">
        <FiAlertTriangle className="w-4 h-4" />
        <span>{error}</span>
      </div>
    )}
    
    {helpText && !error && (
      <p className="text-slate-500 text-sm">{helpText}</p>
    )}
  </div>
);

// Form Checkbox component
export const FormCheckbox = ({ 
  id, 
  label, 
  checked, 
  onChange, 
  helpText, 
  tooltip, 
  error,
  disabled = false
}) => (
  <div className="space-y-2">
    <div className="flex items-start gap-3">
      <input
        type="checkbox"
        id={id}
        name={id}
        checked={checked || false}
        onChange={onChange}
        disabled={disabled}
        className={`
          mt-1 w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded 
          focus:ring-blue-500 focus:ring-2 transition-all duration-300
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      />
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <label htmlFor={id} className={`text-sm font-medium text-slate-300 ${disabled ? 'opacity-50' : 'cursor-pointer'}`}>
            {label}
          </label>
          {tooltip && (
            <Tooltip content={tooltip}>
              <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
            </Tooltip>
          )}
        </div>
        
        {error && (
          <div className="flex items-center gap-2 text-red-400 text-sm mt-1">
            <FiAlertTriangle className="w-4 h-4" />
            <span>{error}</span>
          </div>
        )}
        
        {helpText && !error && (
          <p className="text-slate-500 text-sm mt-1">{helpText}</p>
        )}
      </div>
    </div>
  </div>
);

// Section Header component
export const SectionHeader = ({ title, description, icon: Icon }) => (
  <div className="mb-6 pb-4 border-b border-slate-700">
    <div className="flex items-center gap-3 mb-2">
      {Icon && <Icon className="w-5 h-5 text-blue-400" />}
      <h3 className="text-lg font-semibold text-white">{title}</h3>
    </div>
    {description && (
      <p className="text-slate-400 text-sm">{description}</p>
    )}
  </div>
);
