import React, { useCallback } from 'react';
import { FormInput, FormTextarea, SectionHeader } from '../components/FormComponents';

const NDABasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="NDA Parties & Purpose" description="Who and why" />
      <FormInput
        id="disclosingParty"
        label="Disclosing Party"
        value={formData.disclosingParty}
        onChange={handleChange}
        error={validationErrors.disclosingParty}
        required
      />
      <FormTextarea
        id="purpose"
        label="Purpose of Disclosure"
        value={formData.purpose}
        onChange={handleChange}
        error={validationErrors.purpose}
        required
      />
    </div>
  );
};

export default NDABasicStep;
