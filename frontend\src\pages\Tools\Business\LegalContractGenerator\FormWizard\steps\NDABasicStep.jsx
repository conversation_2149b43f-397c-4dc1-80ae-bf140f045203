import React, { useCallback } from 'react';
import { FormInput, FormTextarea, SectionHeader } from '../components/FormComponents';

const NDABasicStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="NDA Parties & Purpose" description="Who and why" />

      <FormInput
        id="disclosingParty"
        label="Disclosing Party"
        placeholder="Name of party sharing confidential information"
        value={formData.disclosingParty}
        onChange={handleChange}
        helpText="The party that will be sharing confidential information"
        maxLength={100}
        error={validationErrors.disclosingParty}
        required
      />

      <FormInput
        id="receivingParty"
        label="Receiving Party"
        placeholder="Name of party receiving confidential information"
        value={formData.receivingParty}
        onChange={handleChange}
        helpText="The party that will be receiving confidential information"
        maxLength={100}
        error={validationErrors.receivingParty}
        required
      />

      <FormTextarea
        id="purpose"
        label="Purpose of Disclosure"
        placeholder="Describe the purpose for sharing confidential information..."
        value={formData.purpose}
        onChange={handleChange}
        helpText="Explain why confidential information needs to be shared"
        maxLength={500}
        rows={4}
        error={validationErrors.purpose}
        required
      />
    </div>
  );
};

export default NDABasicStep;
