import React, { useCallback } from 'react';
import { FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipManagementStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Management Structure" description="Roles and decision making" />
      <FormTextarea
        id="managementStructure"
        label="Management Structure"
        value={formData.managementStructure}
        onChange={handleChange}
        error={validationErrors.managementStructure}
        required
      />
    </div>
  );
};

export default PartnershipManagementStep;
