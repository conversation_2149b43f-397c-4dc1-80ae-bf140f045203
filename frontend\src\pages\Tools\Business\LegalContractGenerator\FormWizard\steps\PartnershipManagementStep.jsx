import React, { useCallback } from 'react';
import { FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipManagementStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Management Structure" description="Roles and decision making" />

      <FormTextarea
        id="managementStructure"
        label="Management Structure"
        placeholder="Describe the overall management structure of the partnership..."
        value={formData.managementStructure}
        onChange={handleChange}
        helpText="Define how the partnership will be managed and organized"
        maxLength={500}
        rows={4}
        error={validationErrors.managementStructure}
        required
      />

      <FormTextarea
        id="decisionMaking"
        label="Decision Making Process"
        placeholder="Describe how business decisions will be made..."
        value={formData.decisionMaking}
        onChange={handleChange}
        helpText="Define voting procedures, consensus requirements, and authority levels"
        maxLength={500}
        rows={4}
        error={validationErrors.decisionMaking}
        required
      />

      <FormTextarea
        id="rolesResponsibilities"
        label="Roles and Responsibilities"
        placeholder="Detail each partner's specific roles and responsibilities..."
        value={formData.rolesResponsibilities}
        onChange={handleChange}
        helpText="Clearly define what each partner will be responsible for"
        maxLength={1000}
        rows={6}
        error={validationErrors.rolesResponsibilities}
        required
      />
    </div>
  );
};

export default PartnershipManagementStep;
