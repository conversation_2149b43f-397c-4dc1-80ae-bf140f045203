// controllers/tools/BusinessPlan/legalContractController.js
import { generateContent } from '../../../services/geminiBusinessService.js';
import { buildLegalContractPrompt } from './legalContractPrompts.js';
import User from '../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    shouldEnforceLimit,
    getLimit,
} from '../../common/user/limit/planConfig.js';

/**
 * Generate a legal contract based on user input
 * @route POST /api/legal-contracts/generate
 * @access Private
 */
export const generateLegalContract = async (req, res) => {
    try {
        console.log('[LEGAL CONTRACT CONTROLLER] Starting contract generation...');
        
        // 1. Validate request body
        const {
            contractType,
            language = 'English',
            jurisdiction = 'United States',
            ...formData
        } = req.body;

        if (!contractType) {
            return res.status(400).json({
                success: false,
                error: 'Contract type is required'
            });
        }

        // Validate contract type
        const validContractTypes = ['service', 'partnership', 'nda', 'freelance', 'supplier', 'employment', 'lease'];
        if (!validContractTypes.includes(contractType)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid contract type'
            });
        }

        // 2. Get user and check subscription limits
        const userId = req.user.id;
        const user = await User.findById(userId);
        
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }

        // Ensure user has subscription object
        const userWithSub = ensureSubscription(user);
        const planName = userWithSub.subscription.plan;

        // Check usage limits
        if (shouldEnforceLimit(planName)) {
            const limit = getLimit(planName, 'legalContracts');
            const currentCount = planName === FREE_TIER_PLAN_NAME_BACKEND 
                ? (userWithSub.subscription.freeTierLegalContractCount || 0)
                : (userWithSub.subscription.proTierLegalContractCount || 0);

            if (currentCount >= limit) {
                return res.status(429).json({
                    success: false,
                    error: `You have reached your ${planName} plan limit of ${limit} legal contracts. Please upgrade your plan to generate more contracts.`,
                    limit: limit,
                    currentCount: currentCount
                });
            }
        }

        // 3. Validate required fields based on contract type
        const validationError = validateContractData(contractType, formData);
        if (validationError) {
            return res.status(400).json({
                success: false,
                error: validationError
            });
        }

        // 4. Generate the contract
        console.log(`[LEGAL CONTRACT CONTROLLER] Generating ${contractType} contract for user: ${user.email}`);
        
        const contractData = {
            contractType,
            language,
            jurisdiction,
            ...formData
        };

        const prompt = buildLegalContractPrompt(contractData);
        const generatedContract = await generateContent(prompt);

        // 5. Increment usage count and save user (only on success)
        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            userWithSub.subscription.freeTierLegalContractCount = (userWithSub.subscription.freeTierLegalContractCount || 0) + 1;
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            userWithSub.subscription.proTierLegalContractCount = (userWithSub.subscription.proTierLegalContractCount || 0) + 1;
        }

        await userWithSub.save();

        // 6. Return the generated contract
        console.log('[LEGAL CONTRACT CONTROLLER] Contract generated successfully');
        
        res.status(200).json({
            success: true,
            contract: generatedContract,
            contractType: contractType,
            metadata: {
                language: language,
                jurisdiction: jurisdiction,
                generatedAt: new Date().toISOString()
            },
            subscription: {
                plan: userWithSub.subscription.plan,
                freeTierLegalContractCount: userWithSub.subscription.freeTierLegalContractCount || 0,
                proTierLegalContractCount: userWithSub.subscription.proTierLegalContractCount || 0
            }
        });

    } catch (error) {
        console.error('[LEGAL CONTRACT CONTROLLER] Error generating contract:', error);
        
        res.status(500).json({
            success: false,
            error: 'Failed to generate legal contract. Please try again.',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};

/**
 * Validate contract data based on contract type
 */
const validateContractData = (contractType, formData) => {
    // Common required fields
    const commonRequired = ['partyOneName', 'partyTwoName', 'effectiveDate'];
    
    // Contract-specific required fields
    const specificRequired = {
        service: [...commonRequired, 'scopeOfWork', 'paymentAmount'],
        partnership: [...commonRequired, 'businessName', 'businessPurpose'],
        nda: [...commonRequired, 'purpose', 'confidentialInfo'],
        freelance: [...commonRequired, 'projectTitle', 'workDescription', 'paymentRate'],
        supplier: [...commonRequired, 'productsServices', 'pricing'],
        employment: [...commonRequired, 'jobTitle', 'salary', 'jobDescription'],
        lease: [...commonRequired, 'propertyAddress', 'monthlyRent']
    };

    const requiredFields = specificRequired[contractType] || commonRequired;

    for (const field of requiredFields) {
        if (!formData[field] || formData[field].toString().trim().length === 0) {
            return `Missing required field: ${field}`;
        }
    }

    // Validate email fields if present
    const emailFields = ['partyOneEmail', 'partyTwoEmail'];
    for (const field of emailFields) {
        if (formData[field] && !isValidEmail(formData[field])) {
            return `Invalid email format for ${field}`;
        }
    }

    // Validate numeric fields
    const numericFields = {
        service: ['paymentAmount'],
        freelance: ['paymentRate'],
        supplier: [],
        employment: ['salary'],
        lease: ['monthlyRent', 'securityDeposit'],
        partnership: ['capitalContribution']
    };

    const contractNumericFields = numericFields[contractType] || [];
    for (const field of contractNumericFields) {
        if (formData[field] && (isNaN(formData[field]) || parseFloat(formData[field]) <= 0)) {
            return `${field} must be a positive number`;
        }
    }

    return null;
};

/**
 * Validate email format
 */
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * Get contract generation statistics for a user
 * @route GET /api/legal-contracts/stats
 * @access Private
 */
export const getContractStats = async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await User.findById(userId);
        
        if (!user) {
            return res.status(404).json({
                success: false,
                error: 'User not found'
            });
        }

        const userWithSub = ensureSubscription(user);
        const planName = userWithSub.subscription.plan;

        res.status(200).json({
            success: true,
            stats: {
                plan: planName,
                freeTierCount: userWithSub.subscription.freeTierLegalContractCount || 0,
                proTierCount: userWithSub.subscription.proTierLegalContractCount || 0,
                limits: {
                    freeTier: getLimit(FREE_TIER_PLAN_NAME_BACKEND, 'legalContracts'),
                    proTier: getLimit(PRO_PLAN_NAME_BACKEND, 'legalContracts')
                }
            }
        });

    } catch (error) {
        console.error('[LEGAL CONTRACT CONTROLLER] Error getting stats:', error);
        
        res.status(500).json({
            success: false,
            error: 'Failed to get contract statistics'
        });
    }
};
