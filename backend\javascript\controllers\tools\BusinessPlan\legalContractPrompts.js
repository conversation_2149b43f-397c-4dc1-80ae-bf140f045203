// Legal Contract AI Prompts for different contract types

/**
 * Build the main prompt for legal contract generation
 */
export const buildLegalContractPrompt = (contractData) => {
    const { contractType, language, jurisdiction } = contractData;
    
    const basePrompt = getBaseContractPrompt(language, jurisdiction);
    const specificPrompt = getContractSpecificPrompt(contractType, contractData);
    const formatPrompt = getFormatPrompt(contractType);
    
    return `${basePrompt}\n\n${specificPrompt}\n\n${formatPrompt}`;
};

/**
 * Base prompt for all contract types
 */
const getBaseContractPrompt = (language, jurisdiction) => {
    return `You are an expert legal contract drafting AI with extensive knowledge of contract law and legal best practices. 

IMPORTANT INSTRUCTIONS:
- Generate a professional, legally sound contract in ${language}
- Ensure compliance with ${jurisdiction} legal standards and requirements
- Use clear, precise legal language appropriate for the jurisdiction
- Include all necessary legal clauses and protections
- Structure the contract with proper headings, sections, and numbering
- Ensure the contract is comprehensive yet readable
- Include appropriate legal disclaimers and standard clauses
- Use professional formatting with clear section breaks

LEGAL STANDARDS:
- Follow ${jurisdiction} contract law requirements
- Include proper consideration clauses
- Ensure mutual obligations are clearly defined
- Include appropriate dispute resolution mechanisms
- Add standard legal protections (force majeure, severability, etc.)
- Use jurisdiction-appropriate legal terminology`;
};

/**
 * Contract-specific prompts based on contract type
 */
const getContractSpecificPrompt = (contractType, contractData) => {
    const generators = {
        service: generateServiceAgreementPrompt,
        partnership: generatePartnershipAgreementPrompt,
        nda: generateNDAPrompt,
        freelance: generateFreelanceAgreementPrompt,
        supplier: generateSupplierAgreementPrompt,
        employment: generateEmploymentContractPrompt,
        lease: generateLeaseAgreementPrompt
    };
    
    const generator = generators[contractType];
    return generator ? generator(contractData) : generateGenericContractPrompt(contractData);
};

/**
 * Service Agreement specific prompt
 */
const generateServiceAgreementPrompt = (data) => {
    return `CONTRACT TYPE: SERVICE AGREEMENT

Generate a comprehensive Service Agreement with the following details:

PARTIES:
- Service Provider: ${data.partyOneName}
- Client: ${data.partyTwoName}
- Effective Date: ${data.effectiveDate}

CONTRACT DETAILS:
- Title: ${data.contractTitle || 'Service Agreement'}
- Service Provider Email: ${data.partyOneEmail || 'Not provided'}
- Service Provider Address: ${data.partyOneAddress || 'Not provided'}
- Client Email: ${data.partyTwoEmail || 'Not provided'}
- Client Address: ${data.partyTwoAddress || 'Not provided'}

SCOPE OF WORK:
${data.scopeOfWork || 'To be defined'}

DELIVERABLES:
${data.deliverables || 'To be specified'}

PROJECT TIMELINE:
${data.timeline || 'To be established'}

PAYMENT TERMS:
- Total Amount: $${data.paymentAmount || 'TBD'}
- Payment Schedule: ${data.paymentSchedule || 'To be agreed'}
- Payment Terms: ${data.paymentTerms || 'Standard terms apply'}

ADDITIONAL TERMS:
- Change Request Policy: ${data.changeRequestPolicy || 'Standard change management applies'}
- Cancellation Policy: ${data.cancellationPolicy || 'Standard cancellation terms apply'}
- Intellectual Property: ${data.intellectualProperty || 'Standard IP terms apply'}
- Termination Clause: ${data.terminationClause || 'Standard termination terms apply'}
- Warranties: ${data.warranties || 'Standard warranties apply'}
- Liability Limitation: ${data.liabilityLimitation || 'Standard liability limitations apply'}

OPTIONAL CLAUSES:
- Include Confidentiality: ${data.includeConfidentiality ? 'Yes' : 'No'}
- Include Indemnification: ${data.includeIndemnification ? 'Yes' : 'No'}
- Include Force Majeure: ${data.includeForcemajeure ? 'Yes' : 'No'}

Additional Terms: ${data.additionalTerms || 'None specified'}

REQUIRED SECTIONS:
1. Parties and Recitals
2. Scope of Work and Services
3. Deliverables and Timeline
4. Payment Terms and Conditions
5. Intellectual Property Rights
6. Confidentiality (if requested)
7. Termination and Cancellation
8. Warranties and Representations
9. Limitation of Liability
10. Indemnification (if requested)
11. Force Majeure (if requested)
12. General Provisions (Governing Law, Severability, Entire Agreement, etc.)
13. Signature Blocks`;
};

/**
 * Partnership Agreement specific prompt
 */
const generatePartnershipAgreementPrompt = (data) => {
    return `CONTRACT TYPE: PARTNERSHIP AGREEMENT

Generate a comprehensive Partnership Agreement with the following details:

PARTNERSHIP INFORMATION:
- Partnership Name: ${data.businessName || 'Partnership Name TBD'}
- Partnership Type: ${data.businessType || 'General Partnership'}
- Business Purpose: ${data.businessPurpose || 'To be defined'}
- Duration: ${data.partnershipDuration || 'Indefinite'}
${data.partnershipEndDate ? `- End Date: ${data.partnershipEndDate}` : ''}

PARTNERS:
- Partner 1: ${data.partyOneName}
- Partner 2: ${data.partyTwoName}
- Effective Date: ${data.effectiveDate}

FINANCIAL TERMS:
- Initial Capital Contribution: $${data.capitalContribution || 'TBD'}
- Profit Sharing: ${data.profitSharingRatio || 'Equal sharing'}
- Loss Distribution: ${data.lossDistribution || 'Equal sharing'}

MANAGEMENT:
- Management Structure: ${data.managementStructure || 'To be defined'}
- Decision Making: ${data.decisionMaking || 'Mutual consent required'}
- Roles and Responsibilities: ${data.rolesResponsibilities || 'To be specified'}

LEGAL TERMS:
- Exit Strategy: ${data.exitStrategy || 'Standard exit provisions'}
- Dispute Resolution: ${data.disputeResolution || 'Mediation and arbitration'}

REQUIRED SECTIONS:
1. Formation and Name
2. Purpose and Scope
3. Term and Duration
4. Capital Contributions
5. Profit and Loss Sharing
6. Management and Decision Making
7. Partner Rights and Responsibilities
8. Books and Records
9. Withdrawal and Dissolution
10. Dispute Resolution
11. General Provisions`;
};

/**
 * NDA specific prompt
 */
const generateNDAPrompt = (data) => {
    return `CONTRACT TYPE: NON-DISCLOSURE AGREEMENT (NDA)

Generate a comprehensive Non-Disclosure Agreement with the following details:

PARTIES:
- Disclosing Party: ${data.disclosingParty || data.partyOneName}
- Receiving Party: ${data.receivingParty || data.partyTwoName}
- Effective Date: ${data.effectiveDate}

PURPOSE:
${data.purpose || 'Protection of confidential information'}

CONFIDENTIAL INFORMATION:
${data.confidentialInfo || 'All proprietary and confidential information'}

EXCEPTIONS:
${data.exceptions || 'Standard exceptions apply'}

OBLIGATIONS:
${data.obligations || 'Standard confidentiality obligations'}

RESTRICTIONS:
${data.restrictions || 'Standard use restrictions'}

DURATION:
${data.duration || 'Indefinite'}

RETURN OF MATERIALS:
${data.returnOfMaterials || 'Upon request or termination'}

REQUIRED SECTIONS:
1. Parties and Purpose
2. Definition of Confidential Information
3. Obligations of Receiving Party
4. Exceptions to Confidentiality
5. Term and Duration
6. Return of Materials
7. Remedies and Enforcement
8. General Provisions`;
};

/**
 * Generic contract prompt for other types
 */
const generateGenericContractPrompt = (data) => {
    return `Generate a professional legal contract with the provided information:

PARTIES:
- Party 1: ${data.partyOneName}
- Party 2: ${data.partyTwoName}
- Effective Date: ${data.effectiveDate}

CONTRACT TITLE: ${data.contractTitle || 'Legal Agreement'}

Include all relevant sections appropriate for this type of agreement, ensuring legal compliance and professional formatting.`;
};

/**
 * Freelance Agreement prompt
 */
const generateFreelanceAgreementPrompt = (data) => {
    return `CONTRACT TYPE: FREELANCE/CONTRACTOR AGREEMENT

Generate a comprehensive Freelance Agreement with the following details:

PROJECT INFORMATION:
- Project Title: ${data.projectTitle || 'Freelance Project'}
- Contractor: ${data.contractorName || data.partyOneName}
- Client: ${data.clientName || data.partyTwoName}
- Effective Date: ${data.effectiveDate}

WORK DESCRIPTION:
${data.workDescription || 'Work description to be provided'}

DELIVERABLES:
${data.deliverables || 'Deliverables to be specified'}

TIMELINE:
${data.timeline || 'Timeline to be established'}

PAYMENT TERMS:
- Rate: $${data.paymentRate || 'TBD'}
- Structure: ${data.paymentStructure || 'To be agreed'}
- Schedule: ${data.paymentSchedule || 'Upon completion'}

LEGAL TERMS:
- Intellectual Property: ${data.intellectualProperty || 'Standard IP terms'}
- Independent Contractor Status: ${data.independentContractor || 'Standard contractor terms'}

REQUIRED SECTIONS:
1. Parties and Independent Contractor Relationship
2. Scope of Work
3. Deliverables and Timeline
4. Compensation and Payment
5. Intellectual Property Rights
6. Confidentiality
7. Termination
8. General Provisions`;
};

/**
 * Supplier Agreement prompt
 */
const generateSupplierAgreementPrompt = (data) => {
    return `CONTRACT TYPE: SUPPLIER/VENDOR AGREEMENT

PARTIES:
- Supplier: ${data.supplierName || data.partyOneName}
- Buyer: ${data.buyerName || data.partyTwoName}

PRODUCTS/SERVICES:
${data.productsServices || 'Products/services to be specified'}

SPECIFICATIONS:
${data.specifications || 'Specifications to be defined'}

PRICING:
${data.pricing || 'Pricing terms to be established'}

DELIVERY TERMS:
${data.deliveryTerms || 'Standard delivery terms'}

QUALITY STANDARDS:
${data.qualityStandards || 'Industry standard quality requirements'}

Include comprehensive supplier agreement sections with quality assurance, warranties, and commercial terms.`;
};

/**
 * Employment Contract prompt
 */
const generateEmploymentContractPrompt = (data) => {
    return `CONTRACT TYPE: EMPLOYMENT CONTRACT

EMPLOYMENT DETAILS:
- Employee: ${data.employeeName || data.partyTwoName}
- Employer: ${data.employerName || data.partyOneName}
- Job Title: ${data.jobTitle || 'Position TBD'}
- Start Date: ${data.startDate || data.effectiveDate}

COMPENSATION:
- Annual Salary: $${data.salary || 'TBD'}
- Pay Frequency: ${data.payFrequency || 'Monthly'}
- Benefits: ${data.benefits || 'Standard benefits package'}

JOB DETAILS:
- Job Description: ${data.jobDescription || 'Job duties to be specified'}
- Working Hours: ${data.workingHours || 'Standard business hours'}
- Work Location: ${data.workLocation || 'Company premises'}

POLICIES:
- Confidentiality: ${data.confidentiality || 'Standard confidentiality terms'}
- Termination Policy: ${data.terminationPolicy || 'Standard termination procedures'}

Include comprehensive employment terms, benefits, policies, and legal protections.`;
};

/**
 * Lease Agreement prompt
 */
const generateLeaseAgreementPrompt = (data) => {
    return `CONTRACT TYPE: LEASE AGREEMENT

PROPERTY DETAILS:
- Landlord: ${data.landlordName || data.partyOneName}
- Tenant: ${data.tenantName || data.partyTwoName}
- Property Address: ${data.propertyAddress || 'Property address TBD'}
- Property Type: ${data.propertyType || 'Residential/Commercial'}

FINANCIAL TERMS:
- Monthly Rent: $${data.monthlyRent || 'TBD'}
- Security Deposit: $${data.securityDeposit || 'TBD'}
- Lease Start: ${data.leaseStartDate || data.effectiveDate}
- Duration: ${data.leaseDuration || 'One year'}

CONDITIONS:
- Use Restrictions: ${data.useRestrictions || 'Standard use restrictions'}
- Maintenance: ${data.maintenanceResponsibilities || 'Standard maintenance terms'}
- Pet Policy: ${data.petPolicy || 'No pets allowed'}

LEGAL TERMS:
- Termination: ${data.terminationClause || 'Standard termination terms'}
- Renewal: ${data.renewalOptions || 'Standard renewal options'}

Include comprehensive lease terms, property conditions, and legal protections.`;
};

/**
 * Format prompt for professional contract output
 */
const getFormatPrompt = (contractType) => {
    return `
OUTPUT FORMAT REQUIREMENTS:

1. Use professional legal document formatting
2. Include proper contract title and header
3. Number all sections and subsections clearly
4. Use formal legal language appropriate for the jurisdiction
5. Include signature blocks at the end
6. Add date and location fields for execution
7. Ensure proper paragraph spacing and structure
8. Include standard legal clauses (severability, governing law, etc.)
9. Make the contract ready for immediate use
10. Ensure all placeholder information is properly integrated

The contract should be complete, professional, and legally sound. Do not include any AI-generated disclaimers or notes about seeking legal advice within the contract text itself - the contract should appear as a finished legal document.

Generate the complete contract now:`;
};
