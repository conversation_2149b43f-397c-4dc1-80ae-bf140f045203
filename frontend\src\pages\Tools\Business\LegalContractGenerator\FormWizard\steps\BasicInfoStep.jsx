import React, { useCallback } from 'react';
import { FiUsers, FiGlobe, FiFileText } from 'react-icons/fi';
import { FormInput, FormSelect, SectionHeader } from '../components/FormComponents';
import { LANGUAGES, JURISDICTIONS } from '../../utils/contractConstants';

const BasicInfoStep = ({ formData, onFormDataChange, validationErrors, contractType }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  // Get the appropriate labels based on contract type
  const getPartyLabels = () => {
    switch (contractType) {
      case 'service':
        return { party1: 'Service Provider', party2: 'Client' };
      case 'partnership':
        return { party1: 'Partner 1', party2: 'Partner 2' };
      case 'nda':
        return { party1: 'Disclosing Party', party2: 'Receiving Party' };
      case 'freelance':
        return { party1: 'Contractor', party2: 'Client' };
      case 'supplier':
        return { party1: 'Supplier', party2: 'Buyer' };
      case 'employment':
        return { party1: 'Employer', party2: 'Employee' };
      case 'lease':
        return { party1: 'Landlord', party2: 'Tenant' };
      default:
        return { party1: 'Party 1', party2: 'Party 2' };
    }
  };

  // Get contract-specific fields based on contract type
  const getContractSpecificFields = () => {
    switch (contractType) {
      case 'freelance':
        return (
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <FormInput
              id="contractorName"
              label="Contractor Name"
              placeholder="Full name of the contractor"
              value={formData.contractorName}
              onChange={handleChange}
              helpText="Legal name of the freelancer/contractor"
              maxLength={100}
              error={validationErrors.contractorName}
              required
            />
            <FormInput
              id="projectTitle"
              label="Project Title"
              placeholder="Brief title for this project"
              value={formData.projectTitle}
              onChange={handleChange}
              helpText="A descriptive title for the freelance project"
              maxLength={100}
              error={validationErrors.projectTitle}
              required
            />
          </div>
        );
      case 'supplier':
        return (
          <div className="mb-6">
            <FormInput
              id="supplierName"
              label="Supplier Company Name"
              placeholder="Full legal name of the supplier company"
              value={formData.supplierName}
              onChange={handleChange}
              helpText="Official registered name of the supplier"
              maxLength={100}
              error={validationErrors.supplierName}
              required
            />
          </div>
        );
      case 'employment':
        return (
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <FormInput
              id="employeeName"
              label="Employee Name"
              placeholder="Full name of the employee"
              value={formData.employeeName}
              onChange={handleChange}
              helpText="Legal name of the person being hired"
              maxLength={100}
              error={validationErrors.employeeName}
              required
            />
            <FormInput
              id="jobTitle"
              label="Job Title"
              placeholder="Official job title/position"
              value={formData.jobTitle}
              onChange={handleChange}
              helpText="The official title for this position"
              maxLength={100}
              error={validationErrors.jobTitle}
              required
            />
          </div>
        );
      default:
        return null;
    }
  };

  const partyLabels = getPartyLabels();

  return (
    <div className="space-y-8">
      {/* Contract Information */}
      <div>
        <SectionHeader 
          title="Contract Information" 
          description="Basic details about this contract"
          icon={FiFileText}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormInput
            id="contractTitle"
            label="Contract Title"
            placeholder="e.g., Service Agreement - Web Development"
            value={formData.contractTitle}
            onChange={handleChange}
            helpText="A descriptive title for this contract"
            tooltip="This will appear as the main heading of your contract. Make it descriptive and professional."
            maxLength={100}
            error={validationErrors.contractTitle}
            required
          />
          <FormInput
            id="effectiveDate"
            label="Effective Date"
            type="date"
            value={formData.effectiveDate}
            onChange={handleChange}
            helpText="When this contract becomes effective"
            tooltip="The date when the contract terms begin to apply. This is usually the signing date or a future start date."
            error={validationErrors.effectiveDate}
            required
          />
        </div>
      </div>

      {/* Contract-Specific Fields */}
      {getContractSpecificFields() && (
        <div>
          <SectionHeader
            title="Contract-Specific Information"
            description="Additional details for this contract type"
            icon={FiFileText}
          />
          {getContractSpecificFields()}
        </div>
      )}

      {/* Party Information */}
      <div>
        <SectionHeader 
          title="Party Information" 
          description="Details about the contracting parties"
          icon={FiUsers}
        />
        
        {/* Party One */}
        <div className="mb-8">
          <h4 className="text-md font-semibold text-blue-400 mb-4">{partyLabels.party1} Information</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <FormInput
              id="partyOneName"
              label={`${partyLabels.party1} Name`}
              placeholder="Full legal name or company name"
              value={formData.partyOneName}
              onChange={handleChange}
              helpText="Enter the full legal name or registered company name"
              tooltip="This should be the exact legal name as it appears on official documents."
              maxLength={100}
              error={validationErrors.partyOneName}
              required
            />
            <FormInput
              id="partyOneEmail"
              label="Email Address"
              type="email"
              placeholder="<EMAIL>"
              value={formData.partyOneEmail}
              onChange={handleChange}
              helpText="Primary contact email address"
              tooltip="This email will be used for contract-related communications."
              error={validationErrors.partyOneEmail}
              required
            />
          </div>
          <div className="mt-4">
            <FormInput
              id="partyOneAddress"
              label="Address"
              placeholder="Full business or residential address"
              value={formData.partyOneAddress}
              onChange={handleChange}
              helpText="Complete address including city, state/province, and postal code"
              tooltip="Include the full address for legal identification and correspondence."
              maxLength={200}
              error={validationErrors.partyOneAddress}
              required
            />
          </div>
        </div>

        {/* Party Two */}
        <div>
          <h4 className="text-md font-semibold text-purple-400 mb-4">{partyLabels.party2} Information</h4>
          <div className="grid md:grid-cols-2 gap-6">
            <FormInput
              id="partyTwoName"
              label={`${partyLabels.party2} Name`}
              placeholder="Full legal name or company name"
              value={formData.partyTwoName}
              onChange={handleChange}
              helpText="Enter the full legal name or registered company name"
              tooltip="This should be the exact legal name as it appears on official documents."
              maxLength={100}
              error={validationErrors.partyTwoName}
              required
            />
            <FormInput
              id="partyTwoEmail"
              label="Email Address"
              type="email"
              placeholder="<EMAIL>"
              value={formData.partyTwoEmail}
              onChange={handleChange}
              helpText="Primary contact email address"
              tooltip="This email will be used for contract-related communications."
              error={validationErrors.partyTwoEmail}
              required
            />
          </div>
          <div className="mt-4">
            <FormInput
              id="partyTwoAddress"
              label="Address"
              placeholder="Full business or residential address"
              value={formData.partyTwoAddress}
              onChange={handleChange}
              helpText="Complete address including city, state/province, and postal code"
              tooltip="Include the full address for legal identification and correspondence."
              maxLength={200}
              error={validationErrors.partyTwoAddress}
              required
            />
          </div>
        </div>
      </div>

      {/* Legal Settings */}
      <div>
        <SectionHeader 
          title="Legal Settings" 
          description="Language and jurisdiction preferences"
          icon={FiGlobe}
        />
        <div className="grid md:grid-cols-2 gap-6">
          <FormSelect
            id="language"
            label="Contract Language"
            value={formData.language}
            onChange={handleChange}
            helpText="Language for the contract content"
            tooltip="The AI will generate the contract in your selected language with appropriate legal terminology."
            error={validationErrors.language}
            required
          >
            <option value="">Select Language</option>
            {LANGUAGES.map(lang => (
              <option key={lang.code} value={lang.code}>
                {lang.label}
              </option>
            ))}
          </FormSelect>
          
          <FormSelect
            id="jurisdiction"
            label="Governing Jurisdiction"
            value={formData.jurisdiction}
            onChange={handleChange}
            helpText="Legal jurisdiction that will govern this contract"
            tooltip="This determines which country's or state's laws will apply to the contract. Choose the jurisdiction most relevant to your business."
            error={validationErrors.jurisdiction}
            required
          >
            <option value="">Select Jurisdiction</option>
            {JURISDICTIONS.map(jurisdiction => (
              <option key={jurisdiction} value={jurisdiction}>
                {jurisdiction}
              </option>
            ))}
          </FormSelect>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoStep;
