import React, { useState, useRef } from 'react';
import { 
  FiDownload, FiCopy, FiEdit3, FiRefreshCw, <PERSON>Check, 
  FiFileText, FiShare2, FiPrinter, FiSave 
} from 'react-icons/fi';
import { CONTRACT_TYPES } from '../utils/contractConstants';

const ContractResult = ({ 
  contract, 
  contractType, 
  formData, 
  onStartOver, 
  onBackToForm 
}) => {
  const [copied, setCopied] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const contractRef = useRef(null);
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  // Copy contract to clipboard
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(contract);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy contract:', err);
    }
  };

  // Download contract as PDF
  const handleDownloadPDF = async () => {
    setIsDownloading(true);
    try {
      // This would integrate with the PDF generation utility
      // For now, we'll create a simple text file
      const blob = new Blob([contract], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${formData.contractTitle || contractTypeInfo.title}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Failed to download contract:', err);
    } finally {
      setIsDownloading(false);
    }
  };

  // Print contract
  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>${formData.contractTitle || contractTypeInfo.title}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
            h1, h2, h3 { color: #333; }
            .contract-content { white-space: pre-wrap; }
          </style>
        </head>
        <body>
          <div class="contract-content">${contract}</div>
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className={`inline-flex items-center justify-center p-4 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-full mb-6 backdrop-blur-sm`}>
          <FiCheck className="w-8 h-8 text-green-400 mr-3" />
          <span className="text-green-400 font-semibold text-lg">
            Contract Generated Successfully
          </span>
        </div>
        <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-green-200 to-blue-200 bg-clip-text text-transparent mb-4">
          Your {contractTypeInfo.title} is Ready
        </h2>
        <p className="text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed">
          Your professional legal contract has been generated with AI-powered clauses and compliance checks. 
          Review, download, or make adjustments as needed.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        <button
          onClick={handleDownloadPDF}
          disabled={isDownloading}
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25"
        >
          {isDownloading ? (
            <FiRefreshCw className="w-5 h-5 animate-spin" />
          ) : (
            <FiDownload className="w-5 h-5" />
          )}
          {isDownloading ? 'Preparing...' : 'Download PDF'}
        </button>

        <button
          onClick={handleCopy}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300"
        >
          {copied ? (
            <>
              <FiCheck className="w-5 h-5 text-green-400" />
              Copied!
            </>
          ) : (
            <>
              <FiCopy className="w-5 h-5" />
              Copy Text
            </>
          )}
        </button>

        <button
          onClick={handlePrint}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300"
        >
          <FiPrinter className="w-5 h-5" />
          Print
        </button>

        <button
          onClick={onBackToForm}
          className="flex items-center gap-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-xl font-semibold transition-all duration-300"
        >
          <FiEdit3 className="w-5 h-5" />
          Edit Contract
        </button>
      </div>

      {/* Contract Display */}
      <div className="bg-white rounded-2xl shadow-2xl border border-slate-200 overflow-hidden mb-8">
        {/* Contract Header */}
        <div className="bg-slate-50 border-b border-slate-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <contractTypeInfo.icon className={`w-6 h-6 ${contractTypeInfo.textColor.replace('text-', 'text-slate-')}`} />
              <div>
                <h3 className="text-xl font-bold text-slate-800">
                  {formData.contractTitle || contractTypeInfo.title}
                </h3>
                <p className="text-slate-600 text-sm">
                  Generated on {new Date().toLocaleDateString()} • {formData.jurisdiction}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <FiFileText className="w-5 h-5 text-slate-500" />
              <span className="text-slate-600 text-sm font-medium">
                Legal Contract
              </span>
            </div>
          </div>
        </div>

        {/* Contract Content */}
        <div 
          ref={contractRef}
          className="p-8 text-slate-800 leading-relaxed"
          style={{ 
            fontFamily: 'Georgia, serif',
            fontSize: '16px',
            lineHeight: '1.8',
            whiteSpace: 'pre-wrap'
          }}
        >
          {contract}
        </div>
      </div>

      {/* Action Footer */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 bg-slate-800/50 border border-slate-700 rounded-xl">
        <div className="text-center sm:text-left">
          <h4 className="text-white font-semibold mb-1">What's Next?</h4>
          <p className="text-slate-400 text-sm">
            Review your contract carefully and consider having it reviewed by a legal professional before signing.
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={onStartOver}
            className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
          >
            <FiRefreshCw className="w-4 h-4" />
            Create New Contract
          </button>
        </div>
      </div>

      {/* Legal Disclaimer */}
      <div className="mt-8 p-6 bg-yellow-500/10 border border-yellow-500/30 rounded-xl">
        <div className="flex items-start gap-3">
          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
          <div>
            <h4 className="text-yellow-400 font-semibold mb-2">Important Legal Notice</h4>
            <p className="text-slate-400 text-sm leading-relaxed">
              This contract was generated using AI technology and should be reviewed by a qualified attorney 
              before execution. While our AI incorporates legal best practices, every situation is unique and 
              may require specific legal considerations. Dosky is not responsible for the legal validity or 
              enforceability of generated contracts.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractResult;
