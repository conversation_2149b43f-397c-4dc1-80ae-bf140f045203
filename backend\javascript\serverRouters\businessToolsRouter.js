// src/serverRouters/businessToolsRouter.js

import express from 'express';
import businessPlanRoutes from '../routes/Tools/Business/businessPlanRoutes.js';
import savedPlanRoutes from '../routes/Tools/Business/savedPlanRoutes.js';
// --- ADD THIS LINE to import your new investor pitch router ---
import investorPitchRoutes from '../routes/Tools/Business/investorPitchRoutes.js';
// --- ADD THIS LINE to import your new business Q&A router ---
import businessQARoutes from '../routes/Tools/Business/businessQARoutes.js';
// --- ADD THIS LINE to import your new legal contract router ---
import legalContractRoutes from '../routes/Tools/Business/legalContractRoutes.js';

const router = express.Router();

// Routes for the main business plan generator
router.use('/business-plan', businessPlanRoutes);

// Routes for saving/loading business plans
router.use('/saved-plans', savedPlanRoutes);

// --- ADD THIS LINE to register the new investor pitch generator ---
// All routes will be prefixed with '/investor-pitch'
// e.g., POST /api/investor-pitch/generate
router.use('/investor-pitch', investorPitchRoutes);

// --- ADD THIS LINE to register the new business Q&A generator ---
// All routes will be prefixed with '/business-qa'
// e.g., POST /api/business-qa/generate
router.use('/business-qa', businessQARoutes);

// --- ADD THIS LINE to register the new legal contract generator ---
// All routes will be prefixed with '/legal-contracts'
// e.g., POST /api/legal-contracts/generate
router.use('/legal-contracts', legalContractRoutes);


export default router;