import React from 'react';
import { FiAlertTriangle, FiRefreshCw, FiHome, FiMail } from 'react-icons/fi';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    const errorId = Date.now().toString();
    
    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Legal Contract Generator Error Boundary:', error, errorInfo);
    }

    // In production, you might want to send this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo, errorId);
    }
  }

  reportError = (error, errorInfo, errorId) => {
    // This would typically send to an error reporting service like Sentry
    const errorReport = {
      errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.error('Error Report:', errorReport);
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportIssue = () => {
    const subject = encodeURIComponent('Legal Contract Generator Error Report');
    const body = encodeURIComponent(`
Error ID: ${this.state.errorId}
Error Message: ${this.state.error?.message}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}

Please describe what you were doing when this error occurred:
[Your description here]
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-slate-900 flex items-center justify-center px-4">
          <div className="max-w-2xl w-full">
            {/* Error Card */}
            <div className="bg-slate-800/50 border border-slate-700 rounded-2xl p-8 text-center">
              {/* Error Icon */}
              <div className="w-20 h-20 bg-red-500/10 border border-red-500/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiAlertTriangle className="w-10 h-10 text-red-400" />
              </div>

              {/* Error Title */}
              <h1 className="text-3xl font-bold text-white mb-4">
                Oops! Something went wrong
              </h1>

              {/* Error Description */}
              <p className="text-slate-400 text-lg mb-6 leading-relaxed">
                We encountered an unexpected error while generating your legal contract. 
                Don't worry - your data is safe and this issue has been logged for our team to review.
              </p>

              {/* Error ID */}
              {this.state.errorId && (
                <div className="bg-slate-700/50 border border-slate-600 rounded-lg p-4 mb-6">
                  <p className="text-slate-300 text-sm">
                    <span className="font-medium">Error ID:</span> {this.state.errorId}
                  </p>
                  <p className="text-slate-500 text-xs mt-1">
                    Please include this ID when reporting the issue
                  </p>
                </div>
              )}

              {/* Error Details (Development Only) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-left bg-slate-900/50 border border-slate-600 rounded-lg p-4 mb-6">
                  <summary className="text-slate-300 font-medium cursor-pointer mb-2">
                    Error Details (Development)
                  </summary>
                  <div className="text-red-400 text-sm font-mono">
                    <p className="mb-2">
                      <strong>Message:</strong> {this.state.error.message}
                    </p>
                    <p className="mb-2">
                      <strong>Stack:</strong>
                    </p>
                    <pre className="whitespace-pre-wrap text-xs bg-slate-800 p-2 rounded overflow-auto max-h-40">
                      {this.state.error.stack}
                    </pre>
                    {this.state.errorInfo && (
                      <>
                        <p className="mb-2 mt-4">
                          <strong>Component Stack:</strong>
                        </p>
                        <pre className="whitespace-pre-wrap text-xs bg-slate-800 p-2 rounded overflow-auto max-h-40">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </>
                    )}
                  </div>
                </details>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={this.handleRetry}
                  className="flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105"
                >
                  <FiRefreshCw className="w-5 h-5" />
                  Try Again
                </button>

                <button
                  onClick={this.handleGoHome}
                  className="flex items-center justify-center gap-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-semibold transition-all duration-300"
                >
                  <FiHome className="w-5 h-5" />
                  Go Home
                </button>

                <button
                  onClick={this.handleReportIssue}
                  className="flex items-center justify-center gap-2 px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-semibold transition-all duration-300"
                >
                  <FiMail className="w-5 h-5" />
                  Report Issue
                </button>
              </div>

              {/* Help Text */}
              <div className="mt-8 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                <h3 className="text-blue-400 font-semibold mb-2">What can you do?</h3>
                <ul className="text-slate-300 text-sm space-y-1 text-left">
                  <li>• Try refreshing the page or clicking "Try Again"</li>
                  <li>• Check your internet connection</li>
                  <li>• Clear your browser cache and cookies</li>
                  <li>• Try using a different browser</li>
                  <li>• Contact our support team if the problem persists</li>
                </ul>
              </div>
            </div>

            {/* Additional Help */}
            <div className="mt-6 text-center">
              <p className="text-slate-500 text-sm">
                Need immediate help? Contact our support team at{' '}
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-blue-400 hover:text-blue-300 transition-colors"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
