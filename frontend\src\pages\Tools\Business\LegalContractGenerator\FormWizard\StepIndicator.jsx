import React from 'react';
import { Fi<PERSON>heck, FiCircle } from 'react-icons/fi';
import { CONTRACT_TYPES } from '../utils/contractConstants';

const StepIndicator = ({ steps, currentStepIndex, contractType, onStepClick }) => {
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between max-w-4xl mx-auto">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isClickable = index < currentStepIndex;

          return (
            <React.Fragment key={step.id}>
              {/* Step Circle */}
              <div
                className={`
                  relative flex flex-col items-center group
                  ${isClickable ? 'cursor-pointer' : 'cursor-default'}
                `}
                onClick={() => isClickable && onStepClick(index)}
              >
                {/* Circle */}
                <div
                  className={`
                    w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300
                    ${isCompleted 
                      ? `${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} shadow-lg shadow-${contractTypeInfo.textColor.split('-')[1]}-500/20` 
                      : isCurrent 
                        ? `border-blue-500 bg-blue-500/10 shadow-lg shadow-blue-500/20`
                        : 'border-slate-600 bg-slate-800'
                    }
                    ${isClickable ? 'hover:scale-110' : ''}
                  `}
                >
                  {isCompleted ? (
                    <FiCheck className={`w-5 h-5 ${contractTypeInfo.textColor}`} />
                  ) : isCurrent ? (
                    <FiCircle className="w-5 h-5 text-blue-400 fill-current" />
                  ) : (
                    <span className="text-slate-400 font-semibold text-sm">{index + 1}</span>
                  )}
                </div>

                {/* Step Label */}
                <div className="mt-3 text-center">
                  <div
                    className={`
                      text-sm font-medium transition-colors duration-300
                      ${isCompleted 
                        ? contractTypeInfo.textColor 
                        : isCurrent 
                          ? 'text-blue-400' 
                          : 'text-slate-500'
                      }
                      ${isClickable ? 'group-hover:text-white' : ''}
                    `}
                  >
                    {step.title}
                  </div>
                  <div className="text-xs text-slate-500 mt-1 max-w-20 leading-tight">
                    {step.description}
                  </div>
                </div>

                {/* Hover Effect */}
                {isClickable && (
                  <div className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-10 bg-white transition-opacity duration-300 pointer-events-none"></div>
                )}
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="flex-1 mx-4">
                  <div
                    className={`
                      h-0.5 transition-all duration-500
                      ${isCompleted 
                        ? `bg-gradient-to-r ${contractTypeInfo.color}` 
                        : 'bg-slate-700'
                      }
                    `}
                  ></div>
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Mobile Step Indicator */}
      <div className="md:hidden mt-6">
        <div className="flex items-center justify-center gap-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`
                w-2 h-2 rounded-full transition-all duration-300
                ${index < currentStepIndex 
                  ? `${contractTypeInfo.textColor.replace('text-', 'bg-')}` 
                  : index === currentStepIndex 
                    ? 'bg-blue-400' 
                    : 'bg-slate-600'
                }
              `}
            ></div>
          ))}
        </div>
        <div className="text-center mt-2">
          <span className="text-slate-400 text-sm">
            {currentStepIndex + 1} of {steps.length}: {steps[currentStepIndex]?.title}
          </span>
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
