import {
  FiUsers, FiShield, FiUser, FiTruck,
  FiBriefcase, FiHome, FiUserCheck
} from 'react-icons/fi';

export const CONTRACT_TYPES = {
  service: {
    id: 'service',
    title: 'Service Agreement',
    description: 'Professional service contracts for consultants, agencies, and service providers',
    icon: FiUsers,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/30',
    textColor: 'text-blue-400',
    features: [
      'Scope of work definition',
      'Payment terms and schedules',
      'Deliverables and timelines',
      'Intellectual property rights',
      'Termination clauses'
    ]
  },
  partnership: {
    id: 'partnership',
    title: 'Partnership Agreement',
    description: 'Comprehensive agreements for business partnerships and joint ventures',
    icon: FiBriefcase,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/30',
    textColor: 'text-purple-400',
    features: [
      'Profit and loss sharing',
      'Decision-making authority',
      'Capital contributions',
      'Exit strategies',
      'Dispute resolution'
    ]
  },
  nda: {
    id: 'nda',
    title: 'Non-Disclosure Agreement',
    description: 'Protect confidential information and trade secrets',
    icon: FiShield,
    color: 'from-red-500 to-orange-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/30',
    textColor: 'text-red-400',
    features: [
      'Confidentiality obligations',
      'Permitted disclosures',
      'Duration of confidentiality',
      'Return of materials',
      'Legal remedies'
    ]
  },
  freelance: {
    id: 'freelance',
    title: 'Freelance/Contractor Agreement',
    description: 'Independent contractor agreements for freelancers and consultants',
    icon: FiUser,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/30',
    textColor: 'text-green-400',
    features: [
      'Independent contractor status',
      'Project specifications',
      'Payment milestones',
      'Work ownership rights',
      'Performance standards'
    ]
  },
  supplier: {
    id: 'supplier',
    title: 'Supplier/Vendor Agreement',
    description: 'Supply chain agreements for goods and services procurement',
    icon: FiTruck,
    color: 'from-yellow-500 to-amber-500',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/30',
    textColor: 'text-yellow-400',
    features: [
      'Supply specifications',
      'Quality standards',
      'Delivery terms',
      'Pricing and payment',
      'Performance guarantees'
    ]
  },
  employment: {
    id: 'employment',
    title: 'Employment Contract',
    description: 'Comprehensive employment agreements for hiring employees',
    icon: FiUserCheck,
    color: 'from-indigo-500 to-blue-500',
    bgColor: 'bg-indigo-500/10',
    borderColor: 'border-indigo-500/30',
    textColor: 'text-indigo-400',
    features: [
      'Job responsibilities',
      'Compensation and benefits',
      'Working conditions',
      'Confidentiality terms',
      'Termination procedures'
    ]
  },
  lease: {
    id: 'lease',
    title: 'Lease Agreement',
    description: 'Property lease agreements for commercial and residential properties',
    icon: FiHome,
    color: 'from-teal-500 to-cyan-500',
    bgColor: 'bg-teal-500/10',
    borderColor: 'border-teal-500/30',
    textColor: 'text-teal-400',
    features: [
      'Property description',
      'Rent and deposit terms',
      'Lease duration',
      'Maintenance responsibilities',
      'Renewal options'
    ]
  }
};

export const LANGUAGES = [
  { code: 'English', label: '🇺🇸 English', direction: 'ltr' },
  { code: 'Spanish', label: '🇪🇸 Español', direction: 'ltr' },
  { code: 'French', label: '🇫🇷 Français', direction: 'ltr' },
  { code: 'German', label: '🇩🇪 Deutsch', direction: 'ltr' },
  { code: 'Italian', label: '🇮🇹 Italiano', direction: 'ltr' },
  { code: 'Portuguese', label: '🇵🇹 Português', direction: 'ltr' },
  { code: 'Arabic', label: '🇸🇦 العربية', direction: 'rtl' },
  { code: 'Chinese', label: '🇨🇳 中文', direction: 'ltr' },
  { code: 'Japanese', label: '🇯🇵 日本語', direction: 'ltr' },
  { code: 'Korean', label: '🇰🇷 한국어', direction: 'ltr' }
];

export const JURISDICTIONS = [
  'United States',
  'United Kingdom',
  'Canada',
  'Australia',
  'European Union',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Netherlands',
  'Singapore',
  'Hong Kong',
  'Other'
];

export const FORM_STEPS = {
  service: [
    { id: 'basic', title: 'Basic Information', description: 'Contract parties and overview' },
    { id: 'scope', title: 'Scope of Work', description: 'Services and deliverables' },
    { id: 'terms', title: 'Terms & Payment', description: 'Timeline and compensation' },
    { id: 'legal', title: 'Legal Terms', description: 'Clauses and conditions' }
  ],
  partnership: [
    { id: 'basic', title: 'Partnership Details', description: 'Partners and business structure' },
    { id: 'financial', title: 'Financial Terms', description: 'Capital and profit sharing' },
    { id: 'management', title: 'Management', description: 'Roles and decision making' },
    { id: 'legal', title: 'Legal Terms', description: 'Exit and dispute resolution' }
  ],
  nda: [
    { id: 'basic', title: 'Parties & Purpose', description: 'Who and why' },
    { id: 'confidential', title: 'Confidential Information', description: 'What is protected' },
    { id: 'obligations', title: 'Obligations', description: 'Duties and restrictions' },
    { id: 'terms', title: 'Terms & Duration', description: 'Timeline and enforcement' }
  ],
  freelance: [
    { id: 'basic', title: 'Project Overview', description: 'Contractor and client details' },
    { id: 'work', title: 'Work Description', description: 'Project scope and deliverables' },
    { id: 'payment', title: 'Payment Terms', description: 'Rates and payment schedule' },
    { id: 'legal', title: 'Legal Terms', description: 'Rights and responsibilities' }
  ],
  supplier: [
    { id: 'basic', title: 'Supplier Details', description: 'Parties and relationship' },
    { id: 'products', title: 'Products/Services', description: 'What will be supplied' },
    { id: 'terms', title: 'Commercial Terms', description: 'Pricing and delivery' },
    { id: 'quality', title: 'Quality & Legal', description: 'Standards and compliance' }
  ],
  employment: [
    { id: 'basic', title: 'Employee Details', description: 'Position and employee info' },
    { id: 'compensation', title: 'Compensation', description: 'Salary and benefits' },
    { id: 'duties', title: 'Duties & Conditions', description: 'Job responsibilities' },
    { id: 'policies', title: 'Policies & Terms', description: 'Company policies and legal terms' }
  ],
  lease: [
    { id: 'basic', title: 'Property & Parties', description: 'Landlord, tenant, and property' },
    { id: 'financial', title: 'Financial Terms', description: 'Rent and deposits' },
    { id: 'conditions', title: 'Lease Conditions', description: 'Rules and responsibilities' },
    { id: 'legal', title: 'Legal Terms', description: 'Termination and legal clauses' }
  ]
};
