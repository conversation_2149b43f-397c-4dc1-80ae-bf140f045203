import React, { useCallback } from 'react';
import { FiTarget, FiPackage, FiClock } from 'react-icons/fi';
import { FormTextarea, SectionHeader } from '../components/FormComponents';

const ServiceScopeStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      {/* Scope of Work */}
      <div>
        <SectionHeader 
          title="Scope of Work" 
          description="Define the services to be provided"
          icon={FiTarget}
        />
        <FormTextarea
          id="scopeOfWork"
          label="Detailed Scope of Work"
          placeholder="Describe in detail the services to be provided, including specific tasks, methodologies, and any limitations..."
          value={formData.scopeOfWork}
          onChange={handleChange}
          helpText="Provide a comprehensive description of all services to be performed"
          tooltip="Be as specific as possible to avoid misunderstandings. Include what IS included and what is NOT included in the scope."
          maxLength={2000}
          rows={6}
          error={validationErrors.scopeOfWork}
          required
          examples="'Development of responsive website with 5 pages, contact form, and CMS integration. Includes design mockups, development, testing, and deployment. Does not include ongoing maintenance or content creation.'"
        />
      </div>

      {/* Deliverables */}
      <div>
        <SectionHeader 
          title="Deliverables" 
          description="Specific outputs and results to be delivered"
          icon={FiPackage}
        />
        <FormTextarea
          id="deliverables"
          label="Expected Deliverables"
          placeholder="List all specific deliverables, documents, files, or outputs that will be provided..."
          value={formData.deliverables}
          onChange={handleChange}
          helpText="Clearly define what the client will receive upon completion"
          tooltip="Include formats, quantities, and any specifications for deliverables. This helps set clear expectations."
          maxLength={1000}
          rows={5}
          error={validationErrors.deliverables}
          required
          examples="'Final website files, source code, design assets (PSD/Figma files), user manual, 2 hours of training, SSL certificate setup'"
        />
      </div>

      {/* Timeline */}
      <div>
        <SectionHeader 
          title="Project Timeline" 
          description="Schedule and milestones for project completion"
          icon={FiClock}
        />
        <FormTextarea
          id="timeline"
          label="Project Timeline & Milestones"
          placeholder="Outline the project schedule, key milestones, and completion dates..."
          value={formData.timeline}
          onChange={handleChange}
          helpText="Include major milestones, review periods, and final completion date"
          tooltip="Break down the project into phases with specific dates. This helps track progress and manage expectations."
          maxLength={500}
          rows={4}
          error={validationErrors.timeline}
          required
          examples="'Week 1-2: Design mockups and approval, Week 3-4: Development phase, Week 5: Testing and revisions, Week 6: Final delivery and training'"
        />
      </div>

      {/* Additional Information */}
      <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-6">
        <h4 className="text-blue-400 font-semibold mb-3 flex items-center gap-2">
          <FiTarget className="w-4 h-4" />
          Pro Tips for Scope Definition
        </h4>
        <ul className="text-slate-300 text-sm space-y-2">
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Be specific about what's included and excluded to prevent scope creep</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Include revision rounds and approval processes in your timeline</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Define deliverable formats and technical specifications clearly</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Consider adding buffer time for unexpected delays or changes</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ServiceScopeStep;
