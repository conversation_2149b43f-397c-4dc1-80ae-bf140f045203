// node_gemini_summarizer/server.js
import dotenv from 'dotenv';
import path from 'path';
import cluster from 'cluster';

const envPath = path.resolve(process.cwd(), '.env');
const configResult = dotenv.config({ path: envPath });

if (configResult.error) {
  console.error(`[Process ${process.pid}, Type: ${cluster.isPrimary ? 'Primary' : `Worker (ID: ${cluster.worker?.id})`}] FATAL ENV ERROR: Could not load .env file from ${envPath}:`, configResult.error);
  process.exit(1);
}

import express from 'express';
import cors from 'cors';
import http from 'http';
import compression from 'compression';
import os from 'os';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import morgan from 'morgan';

import connectDB from './config/db.js';
import SubscriptionLimits from './models/SubscriptionLimits.js';
import planConfigService from './services/planConfigService.js';

// --- Route Imports ---
import authRoutes from './routes/common/authRoutes.js';
import savedItemsRoutes from './routes/common/savedItemsRoutes.js';
import paypalRoutes from './routes/common/paypalRoutes.js';
import userRoutes from './routes/common/userRoutes.js';
import adminRoutes from './routes/admin/adminRoutes.js';

import { protect } from './middleware/authMiddleware.js';
import { errorHandler, notFound } from './middleware/errorHandler.js';
import cacheService from './services/cacheService.js';

// Tool-specific Route Aggregators
import pdfRouter from './serverRouters/Pdf.js';
import businessToolsRouter from './serverRouters/businessToolsRouter.js';

const numCPUs = os.cpus().length;
const port = process.env.PORT || 3001;
const isProduction = process.env.NODE_ENV === 'production';

if (cluster.isPrimary) {
    console.log(`Primary ${process.pid} is starting on port ${port}, Env: ${process.env.NODE_ENV || 'development'}`);

    // --- Primary Process Sanity Checks ---
    if (!process.env.MONGO_URI) {
        console.error(`PRIMARY FATAL ERROR: MONGO_URI is not defined in environment for Primary Process ${process.pid}. Application cannot start.`);
        process.exit(1);
    }
    if (!process.env.JWT_SECRET) {
        console.error(`PRIMARY FATAL ERROR: JWT_SECRET is not defined in environment for Primary Process ${process.pid}. Authentication will fail.`);
        process.exit(1);
    }
    if (!process.env.GEMINI_API_KEY) {
        console.warn(`PRIMARY WARNING (Process ${process.pid}): GEMINI_API_KEY is not set. AI features might not work.`);
    }
    if (!process.env.CLIENT_URL) {
        console.warn(`PRIMARY WARNING (Process ${process.pid}): CLIENT_URL is not set. CORS might default incorrectly.`);
    }

    console.log(`Forking for ${numCPUs} CPUs`);
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
    cluster.on('exit', (worker, code, signal) => {
        console.error(`Worker ${worker.process.pid} died (code: ${code}, signal: ${signal}). Forking another one...`);
        cluster.fork();
    });

} else {
    // --- Worker Process Logic ---
    const app = express();
    const httpServer = http.createServer(app);

    connectDB().then(async () => {
        // Initialize default subscription plans
        try {
            await SubscriptionLimits.initializeDefaultPlans();
            console.log(`WORKER ${process.pid}: Subscription plans initialized`);
        } catch (err) {
            console.error(`WORKER ${process.pid}: Failed to initialize subscription plans:`, err);
        }

        // Initialize plan configuration service
        try {
            await planConfigService.initialize();
            console.log(`WORKER ${process.pid}: Plan configuration service initialized`);
        } catch (err) {
            console.error(`WORKER ${process.pid}: Failed to initialize plan configuration service:`, err);
        }
    }).catch(err => {
        console.error(`WORKER ${process.pid}: Failed during connectDB() execution. Exiting.`, err);
        process.exit(1);
    });

    // --- Setup Middleware ---
    app.use(helmet({
        contentSecurityPolicy: isProduction ? undefined : false,
        crossOriginEmbedderPolicy: false,
        crossOriginResourcePolicy: { policy: "cross-origin" }
    }));
    app.use(compression());

    const corsOptions = {
        origin: process.env.CLIENT_URL || "http://localhost:5173",
        credentials: true,
    };
    app.use(cors(corsOptions));

    app.use(express.json({ limit: process.env.JSON_PAYLOAD_LIMIT || '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: process.env.URLENCODED_PAYLOAD_LIMIT || '10mb' }));

    // Use morgan to log all incoming HTTP requests to the console
    app.use(morgan('dev'));

    // Apply rate limiting to all /api/ routes
    const apiLimiter = rateLimit({
        windowMs: 15 * 60 * 1000,
        max: isProduction ? (parseInt(process.env.PROD_RATE_LIMIT_MAX || "500", 10)) : (parseInt(process.env.DEV_RATE_LIMIT_MAX || "10000", 10)),
        standardHeaders: true,
        legacyHeaders: false,
        message: 'Too many requests from this IP, please try again after 15 minutes',
    });
    app.use('/api/', apiLimiter);

    // --- API Routes ---
    app.use('/api/auth', authRoutes);
    app.use('/api/saved-items', protect, savedItemsRoutes);
    app.use('/api/paypal', protect, paypalRoutes);
    app.use('/api/users', protect, userRoutes);
    app.use('/api/admin', adminRoutes);

    app.use('/api', pdfRouter);
    app.use('/api', businessToolsRouter);

    // Root endpoint for health checks
    app.get('/', (req, res) => {
        res.send(`Node.js PDF Analyzer is running (Worker ${process.pid})!`);
    });

    // --- 404 Handler for undefined routes ---
    app.use(notFound);

    // --- Centralized Error Handler ---
    app.use(errorHandler);

    // Warm up cache with subscription plans
    console.log('Warming up cache...');
    await cacheService.warmUp(SubscriptionLimits);

    const serverInstance = httpServer.listen(port, () => {
        if (!isProduction) {
            console.log(`Worker ${process.pid} started. Node.js server running on http://localhost:${port}`);
            console.log('Cache warmed up and ready');
        }
    });

    // --- Graceful Shutdown Logic ---
    const shutdownWorker = async (signal) => {
        const workerPid = process.pid;
        console.warn(`Worker ${workerPid} received ${signal}. Shutting down gracefully...`);
        try {
            await new Promise((resolve, reject) => {
                serverInstance.close((err) => {
                    if (err) {
                        console.error(`Worker ${workerPid} error closing HTTP server:`, err);
                        return reject(err);
                    }
                    console.log(`Worker ${workerPid} HTTP server closed.`);
                    resolve();
                });
            });
            console.log(`Worker ${workerPid} shut down successfully.`);
            process.exit(0);
        } catch (err) {
            console.error(`Worker ${workerPid} error during graceful shutdown:`, err);
            process.exit(1);
        }
    };

    process.on('SIGINT', () => shutdownWorker('SIGINT'));
    process.on('SIGTERM', () => shutdownWorker('SIGTERM'));
}