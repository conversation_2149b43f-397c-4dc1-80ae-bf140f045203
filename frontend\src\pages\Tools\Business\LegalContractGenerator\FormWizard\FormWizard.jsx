import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rrowR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Loader } from 'react-icons/fi';
import StepIndicator from './StepIndicator';
import FormStep from './FormStep';
import { FORM_STEPS, CONTRACT_TYPES } from '../utils/contractConstants';
import { validateFormStep } from '../utils/formValidation';

const FormWizard = ({ 
  contractType, 
  formData, 
  onFormDataUpdate, 
  onGenerate, 
  onBack, 
  error 
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = FORM_STEPS[contractType] || [];
  const currentStep = steps[currentStepIndex];
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  // Reset step when contract type changes
  useEffect(() => {
    setCurrentStepIndex(0);
    setValidationErrors({});
  }, [contractType]);

  // Handle form data updates
  const handleFormDataChange = useCallback((stepData) => {
    onFormDataUpdate(stepData);
    
    // Clear validation errors for updated fields
    const updatedErrors = { ...validationErrors };
    Object.keys(stepData).forEach(key => {
      delete updatedErrors[key];
    });
    setValidationErrors(updatedErrors);
  }, [onFormDataUpdate, validationErrors]);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const errors = validateFormStep(contractType, currentStep.id, formData);
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [contractType, currentStep, formData]);

  // Handle next step
  const handleNext = useCallback(() => {
    if (validateCurrentStep()) {
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex(prev => prev + 1);
      }
    }
  }, [validateCurrentStep, currentStepIndex, steps.length]);

  // Handle previous step
  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
      setValidationErrors({});
    }
  }, [currentStepIndex]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (validateCurrentStep()) {
      setIsSubmitting(true);
      try {
        await onGenerate(formData);
      } catch (err) {
        console.error('Form submission error:', err);
      } finally {
        setIsSubmitting(false);
      }
    }
  }, [validateCurrentStep, onGenerate, formData]);

  const isLastStep = currentStepIndex === steps.length - 1;
  const isFirstStep = currentStepIndex === 0;

  return (
    <div className="w-full max-w-5xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className={`inline-flex items-center justify-center p-3 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-full mb-4 backdrop-blur-sm`}>
          <contractTypeInfo.icon className={`w-6 h-6 ${contractTypeInfo.textColor} mr-2`} />
          <span className={`${contractTypeInfo.textColor} font-semibold`}>
            {contractTypeInfo.title}
          </span>
        </div>
        <h2 className="text-3xl font-bold text-white mb-2">
          {currentStep?.title}
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          {currentStep?.description}
        </p>
      </div>

      {/* Step Indicator */}
      <StepIndicator
        steps={steps}
        currentStepIndex={currentStepIndex}
        contractType={contractType}
        onStepClick={(index) => {
          if (index < currentStepIndex) {
            setCurrentStepIndex(index);
            setValidationErrors({});
          }
        }}
      />

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Form Step Content */}
      <div className="bg-slate-800/50 border border-slate-700 rounded-2xl p-6 md:p-8 mb-8">
        <FormStep
          contractType={contractType}
          stepId={currentStep?.id}
          formData={formData}
          onFormDataChange={handleFormDataChange}
          validationErrors={validationErrors}
        />
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center">
        <div className="flex gap-4">
          {/* Back to Selection Button */}
          <button
            onClick={onBack}
            className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
          >
            <FiArrowLeft className="w-4 h-4" />
            Change Contract Type
          </button>

          {/* Previous Step Button */}
          {!isFirstStep && (
            <button
              onClick={handlePrevious}
              className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              Previous
            </button>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Step Counter */}
          <span className="text-slate-400 text-sm">
            Step {currentStepIndex + 1} of {steps.length}
          </span>

          {/* Next/Submit Button */}
          {isLastStep ? (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`
                px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 
                text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 
                hover:shadow-xl hover:shadow-blue-500/25 flex items-center gap-3
                ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              {isSubmitting ? (
                <>
                  <FiLoader className="w-5 h-5 animate-spin" />
                  Generating Contract...
                </>
              ) : (
                <>
                  <FiCheck className="w-5 h-5" />
                  Generate Contract
                </>
              )}
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25 flex items-center gap-2"
            >
              Next
              <FiArrowRight className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormWizard;
