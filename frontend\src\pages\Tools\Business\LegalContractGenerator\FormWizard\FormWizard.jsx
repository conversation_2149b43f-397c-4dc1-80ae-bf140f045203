import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON><PERSON>rrow<PERSON><PERSON>t, FiArrowRight, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiAlertTriangle } from 'react-icons/fi';
import StepIndicator from './StepIndicator';
import FormStep from './FormStep';
import { FORM_STEPS, CONTRACT_TYPES } from '../utils/contractConstants';
import { validateFormStep } from '../utils/formValidation';

const FormWizard = ({
  contractType,
  formData,
  onFormDataUpdate,
  onGenerate,
  onBack,
  error
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationSummary, setShowValidationSummary] = useState(false);

  const steps = FORM_STEPS[contractType] || [];
  const currentStep = steps[currentStepIndex];
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  // Safety check for missing contract type or steps
  if (!contractType || !contractTypeInfo) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">Invalid contract type: {contractType}</p>
      </div>
    );
  }

  if (steps.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">No steps defined for contract type: {contractType}</p>
      </div>
    );
  }

  if (!currentStep) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">Current step not found. Step index: {currentStepIndex}, Total steps: {steps.length}</p>
      </div>
    );
  }

  // Reset step when contract type changes
  useEffect(() => {
    setCurrentStepIndex(0);
    setValidationErrors({});
  }, [contractType]);

  // Handle form data updates
  const handleFormDataChange = useCallback((stepData) => {
    onFormDataUpdate(stepData);

    // Clear validation errors for updated fields
    const updatedErrors = { ...validationErrors };
    Object.keys(stepData).forEach(key => {
      delete updatedErrors[key];
    });
    setValidationErrors(updatedErrors);

    // Hide validation summary when user starts typing
    if (showValidationSummary) {
      setShowValidationSummary(false);
    }
  }, [onFormDataUpdate, validationErrors, showValidationSummary]);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    // Check if currentStep exists
    if (!currentStep || !currentStep.id) {
      return false;
    }

    const errors = validateFormStep(contractType, currentStep.id, formData);
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [contractType, currentStep, formData]);

  // Handle next step
  const handleNext = useCallback(() => {
    try {
      const isValid = validateCurrentStep();

      if (isValid) {
        if (currentStepIndex < steps.length - 1) {
          setCurrentStepIndex(prev => prev + 1);
          setShowValidationSummary(false); // Hide validation summary on successful navigation
        }
      } else {
        setShowValidationSummary(true); // Show validation summary when validation fails
        // Auto-hide the summary after 5 seconds
        setTimeout(() => setShowValidationSummary(false), 5000);
      }
    } catch (error) {
      console.error('Error in handleNext:', error);
      setError('An error occurred while navigating to the next step. Please try again.');
    }
  }, [validateCurrentStep, currentStepIndex, steps.length]);

  // Handle previous step
  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
      setValidationErrors({});
    }
  }, [currentStepIndex]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (validateCurrentStep()) {
      setIsSubmitting(true);
      try {
        await onGenerate(formData);
      } catch (err) {
        console.error('Form submission error:', err);
      } finally {
        setIsSubmitting(false);
      }
    }
  }, [validateCurrentStep, onGenerate, formData]);

  const isLastStep = currentStepIndex === steps.length - 1;
  const isFirstStep = currentStepIndex === 0;

  // Convert field names to user-friendly labels
  const getFieldLabel = (fieldName) => {
    const fieldLabels = {
      contractTitle: 'Contract Title',
      effectiveDate: 'Effective Date',
      partyOneName: 'First Party Name',
      partyOneEmail: 'First Party Email',
      partyOneAddress: 'First Party Address',
      partyTwoName: 'Second Party Name',
      partyTwoEmail: 'Second Party Email',
      partyTwoAddress: 'Second Party Address',
      language: 'Contract Language',
      jurisdiction: 'Governing Jurisdiction'
    };
    return fieldLabels[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  };

  return (
    <div className="w-full max-w-5xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-8">
        <div className={`inline-flex items-center justify-center p-3 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-full mb-4 backdrop-blur-sm`}>
          <contractTypeInfo.icon className={`w-6 h-6 ${contractTypeInfo.textColor} mr-2`} />
          <span className={`${contractTypeInfo.textColor} font-semibold`}>
            {contractTypeInfo.title}
          </span>
        </div>
        <h2 className="text-3xl font-bold text-white mb-2">
          {currentStep?.title}
        </h2>
        <p className="text-slate-400 max-w-2xl mx-auto">
          {currentStep?.description}
        </p>
      </div>

      {/* Step Indicator */}
      <StepIndicator
        steps={steps}
        currentStepIndex={currentStepIndex}
        contractType={contractType}
        onStepClick={(index) => {
          if (index < currentStepIndex) {
            setCurrentStepIndex(index);
            setValidationErrors({});
          }
        }}
      />

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Validation Summary */}
      {showValidationSummary && Object.keys(validationErrors).length > 0 && (
        <div className="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-xl">
          <div className="flex items-center gap-2 mb-2">
            <FiAlertTriangle className="w-5 h-5 text-yellow-400" />
            <h4 className="text-yellow-400 font-medium">Please fix the following errors to continue:</h4>
          </div>
          <ul className="text-yellow-300 text-sm space-y-1">
            {Object.entries(validationErrors).map(([field, error]) => (
              <li key={field} className="flex items-start gap-2">
                <span className="text-yellow-500 mt-1">•</span>
                <span><strong>{getFieldLabel(field)}:</strong> {error}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Form Step Content */}
      <div className="bg-slate-800/50 border border-slate-700 rounded-2xl p-6 md:p-8 mb-8">
        <FormStep
          contractType={contractType}
          stepId={currentStep?.id}
          formData={formData}
          onFormDataChange={handleFormDataChange}
          validationErrors={validationErrors}
        />
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center">
        <div className="flex gap-4">
          {/* Back to Selection Button */}
          <button
            onClick={onBack}
            className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
          >
            <FiArrowLeft className="w-4 h-4" />
            Change Contract Type
          </button>

          {/* Previous Step Button */}
          {!isFirstStep && (
            <button
              onClick={handlePrevious}
              className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white rounded-xl font-medium transition-all duration-300 flex items-center gap-2"
            >
              <FiArrowLeft className="w-4 h-4" />
              Previous
            </button>
          )}
        </div>

        <div className="flex items-center gap-4">
          {/* Step Counter */}
          <span className="text-slate-400 text-sm">
            Step {currentStepIndex + 1} of {steps.length}
          </span>

          {/* Next/Submit Button */}
          {isLastStep ? (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className={`
                px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 
                text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 
                hover:shadow-xl hover:shadow-blue-500/25 flex items-center gap-3
                ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              {isSubmitting ? (
                <>
                  <FiLoader className="w-5 h-5 animate-spin" />
                  Generating Contract...
                </>
              ) : (
                <>
                  <FiCheck className="w-5 h-5" />
                  Generate Contract
                </>
              )}
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              Next
              <FiArrowRight className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FormWizard;
