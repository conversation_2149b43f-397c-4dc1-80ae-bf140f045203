import React from 'react';
import BasicInfoStep from './steps/BasicInfoStep';
import ServiceScopeStep from './steps/ServiceScopeStep';
import ServiceTermsStep from './steps/ServiceTermsStep';
import ServiceLegalStep from './steps/ServiceLegalStep';
import PartnershipFinancialStep from './steps/PartnershipFinancialStep';
import PartnershipManagementStep from './steps/PartnershipManagementStep';
import PartnershipLegalStep from './steps/PartnershipLegalStep';
import {
  NDAConfidentialStep,
  NDAObligationsStep,
  NDATermsStep,
  FreelanceWorkStep,
  FreelancePaymentStep,
  FreelanceLegalStep,
  SupplierProductsStep,
  SupplierTermsStep,
  SupplierQualityStep,
  EmploymentCompensationStep,
  EmploymentDutiesStep,
  EmploymentPoliciesStep,
  LeaseFinancialStep,
  LeaseConditionsStep,
  LeaseLegalStep
} from './steps/placeholderSteps.jsx';

const FormStep = ({ contractType, stepId, formData, onFormDataChange, validationErrors }) => {
  const stepKey = `${contractType}_${stepId}`;

  // Map contract types and steps to their respective components
  const stepComponents = {
    // Service Agreement Steps
    service_basic: BasicInfoStep,
    service_scope: ServiceScopeStep,
    service_terms: ServiceTermsStep,
    service_legal: ServiceLegalStep,

    // Partnership Agreement Steps - Use BasicInfoStep for consistent party information
    partnership_basic: BasicInfoStep,
    partnership_financial: PartnershipFinancialStep,
    partnership_management: PartnershipManagementStep,
    partnership_legal: PartnershipLegalStep,

    // NDA Steps - Use BasicInfoStep for consistent party information
    nda_basic: BasicInfoStep,
    nda_confidential: NDAConfidentialStep,
    nda_obligations: NDAObligationsStep,
    nda_terms: NDATermsStep,

    // Freelance Agreement Steps - Use BasicInfoStep for consistent party information
    freelance_basic: BasicInfoStep,
    freelance_work: FreelanceWorkStep,
    freelance_payment: FreelancePaymentStep,
    freelance_legal: FreelanceLegalStep,

    // Supplier Agreement Steps - Use BasicInfoStep for consistent party information
    supplier_basic: BasicInfoStep,
    supplier_products: SupplierProductsStep,
    supplier_terms: SupplierTermsStep,
    supplier_quality: SupplierQualityStep,

    // Employment Contract Steps - Use BasicInfoStep for consistent party information
    employment_basic: BasicInfoStep,
    employment_compensation: EmploymentCompensationStep,
    employment_duties: EmploymentDutiesStep,
    employment_policies: EmploymentPoliciesStep,

    // Lease Agreement Steps - Use BasicInfoStep for consistent party information
    lease_basic: BasicInfoStep,
    lease_financial: LeaseFinancialStep,
    lease_conditions: LeaseConditionsStep,
    lease_legal: LeaseLegalStep
  };

  const StepComponent = stepComponents[stepKey];

  if (!StepComponent) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-400">Step component not found for {stepKey}</p>
      </div>
    );
  }

  return (
    <div className="animate-fade-in">
      <StepComponent
        formData={formData}
        onFormDataChange={onFormDataChange}
        validationErrors={validationErrors}
        contractType={contractType}
      />
    </div>
  );
};

export default FormStep;
