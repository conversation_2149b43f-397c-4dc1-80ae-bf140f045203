// routes/Tools/Business/legalContractRoutes.js

import express from 'express';
import { 
    generateLegalContract, 
    getContractStats 
} from '../../../controllers/tools/BusinessPlan/legalContractController.js';
import { protect } from '../../../middleware/authMiddleware.js';

const router = express.Router();

// @desc    Generate a legal contract based on user input
// @route   POST /api/legal-contracts/generate
// @access  Private (requires authentication)
router.post('/generate', protect, generateLegalContract);

// @desc    Get contract generation statistics for the authenticated user
// @route   GET /api/legal-contracts/stats
// @access  Private (requires authentication)
router.get('/stats', protect, getContractStats);

export default router;
