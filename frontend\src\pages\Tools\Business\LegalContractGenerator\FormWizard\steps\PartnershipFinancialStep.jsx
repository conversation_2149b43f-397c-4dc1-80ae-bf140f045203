import React, { useCallback } from 'react';
import { FormInput, FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipFinancialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Financial Terms" description="Capital contributions and profit sharing" />

      <FormInput
        id="capitalContribution"
        label="Initial Capital Contribution"
        type="number"
        placeholder="10000"
        value={formData.capitalContribution}
        onChange={handleChange}
        helpText="Amount each partner will contribute initially"
        error={validationErrors.capitalContribution}
        required
      />

      <FormTextarea
        id="profitSharingRatio"
        label="Profit Sharing Arrangement"
        placeholder="Describe how profits will be shared between partners..."
        value={formData.profitSharingRatio}
        onChange={handleChange}
        helpText="Define the profit distribution method and percentages"
        maxLength={200}
        rows={3}
        error={validationErrors.profitSharingRatio}
        required
      />

      <FormTextarea
        id="lossDistribution"
        label="Loss Distribution"
        placeholder="Describe how losses will be shared between partners..."
        value={formData.lossDistribution}
        onChange={handleChange}
        helpText="Define how business losses will be allocated"
        maxLength={200}
        rows={3}
        error={validationErrors.lossDistribution}
        required
      />
    </div>
  );
};

export default PartnershipFinancialStep;
