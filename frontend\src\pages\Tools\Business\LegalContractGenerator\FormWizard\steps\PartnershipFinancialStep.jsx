import React, { useCallback } from 'react';
import { FormInput, FormTextarea, SectionHeader } from '../components/FormComponents';

const PartnershipFinancialStep = ({ formData, onFormDataChange, validationErrors }) => {
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    onFormDataChange({ [name]: value });
  }, [onFormDataChange]);

  return (
    <div className="space-y-8">
      <SectionHeader title="Financial Terms" description="Capital contributions and profit sharing" />
      <FormInput
        id="capitalContribution"
        label="Initial Capital Contribution"
        type="number"
        value={formData.capitalContribution}
        onChange={handleChange}
        error={validationErrors.capitalContribution}
        required
      />
      <FormTextarea
        id="profitSharingRatio"
        label="Profit Sharing Arrangement"
        value={formData.profitSharingRatio}
        onChange={handleChange}
        error={validationErrors.profitSharingRatio}
        required
      />
    </div>
  );
};

export default PartnershipFinancialStep;
