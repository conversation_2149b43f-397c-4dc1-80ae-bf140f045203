import React, { useState, useEffect } from 'react';
import { FiInfo, FiAlertTriangle, FiLightbulb, FiCheck, FiX } from 'react-icons/fi';
import { getSmartSuggestions, getContextualHelp, validateFieldWithAI } from '../../utils/aiIntegration';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../../../../store/features/auth/authSlice';

// Enhanced Form Textarea with AI suggestions
export const EnhancedFormTextarea = ({ 
  id, 
  label, 
  placeholder, 
  value, 
  onChange, 
  helpText, 
  maxLength, 
  tooltip, 
  examples,
  error,
  required = false,
  rows = 4,
  disabled = false,
  contractType,
  formData
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [aiValidation, setAiValidation] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const token = useSelector(selectAuthToken);

  const suggestions = getSmartSuggestions(contractType, id, value, formData);
  const contextualHelp = getContextualHelp(contractType, id);

  // AI validation on blur
  const handleBlur = async () => {
    if (value && value.length > 20 && token) {
      setIsValidating(true);
      try {
        const validation = await validateFieldWithAI(id, value, contractType, 'field_validation', token);
        setAiValidation(validation);
      } catch (error) {
        console.error('AI validation error:', error);
      } finally {
        setIsValidating(false);
      }
    }
  };

  const handleSuggestionClick = (suggestion) => {
    const event = {
      target: {
        name: id,
        value: suggestion
      }
    };
    onChange(event);
    setShowSuggestions(false);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <label htmlFor={id} className="block text-sm font-medium text-slate-300">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
        {tooltip && (
          <div className="group relative inline-block">
            <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
            <div className="invisible group-hover:visible absolute z-10 w-64 p-3 mt-2 text-sm text-white bg-slate-800 border border-slate-600 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 left-0">
              {tooltip}
            </div>
          </div>
        )}
        {suggestions.length > 0 && (
          <button
            type="button"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="flex items-center gap-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
          >
            <FiLightbulb className="w-3 h-3" />
            AI Suggestions
          </button>
        )}
      </div>
      
      <div className="relative">
        <textarea
          id={id}
          name={id}
          placeholder={placeholder}
          value={value || ''}
          onChange={onChange}
          onBlur={handleBlur}
          maxLength={maxLength}
          rows={rows}
          disabled={disabled}
          className={`
            w-full px-4 py-3 bg-slate-700/50 border rounded-xl text-white placeholder-slate-400 
            transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
            resize-vertical min-h-[100px]
            ${error ? 'border-red-500 bg-red-500/10' : 'border-slate-600 hover:border-slate-500'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${isValidating ? 'border-yellow-500' : ''}
          `}
        />
        
        {isValidating && (
          <div className="absolute top-3 right-3">
            <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>

      {/* AI Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="bg-slate-800 border border-slate-600 rounded-lg shadow-lg p-3 space-y-2">
          <div className="flex items-center gap-2 text-blue-400 text-sm font-medium">
            <FiLightbulb className="w-4 h-4" />
            AI Suggestions
          </div>
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full text-left p-2 text-sm text-slate-300 hover:text-white hover:bg-slate-700 rounded transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}

      {/* AI Validation Results */}
      {aiValidation && (
        <div className="space-y-2">
          {aiValidation.suggestions.length > 0 && (
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-400 text-sm font-medium mb-2">
                <FiLightbulb className="w-4 h-4" />
                AI Suggestions
              </div>
              <ul className="text-sm text-blue-300 space-y-1">
                {aiValidation.suggestions.map((suggestion, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    {suggestion}
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {aiValidation.warnings.length > 0 && (
            <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
              <div className="flex items-center gap-2 text-yellow-400 text-sm font-medium mb-2">
                <FiAlertTriangle className="w-4 h-4" />
                Potential Issues
              </div>
              <ul className="text-sm text-yellow-300 space-y-1">
                {aiValidation.warnings.map((warning, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    {warning}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
      
      {error && (
        <div className="flex items-center gap-2 text-red-400 text-sm">
          <FiAlertTriangle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}
      
      {helpText && !error && (
        <p className="text-slate-500 text-sm">{helpText}</p>
      )}
      
      {contextualHelp && !error && !helpText && (
        <p className="text-slate-500 text-sm italic">{contextualHelp}</p>
      )}
      
      {examples && !error && (
        <div className="text-slate-500 text-xs">
          <span className="font-medium">Examples:</span> {examples}
        </div>
      )}
      
      {maxLength && (
        <div className="text-right text-xs text-slate-500">
          {value ? value.length : 0}/{maxLength}
        </div>
      )}
    </div>
  );
};

// Smart Input with AI validation
export const SmartFormInput = ({ 
  id, 
  label, 
  type = 'text',
  placeholder, 
  value, 
  onChange, 
  helpText, 
  maxLength, 
  tooltip, 
  examples,
  error,
  required = false,
  disabled = false,
  contractType,
  formData
}) => {
  const [aiValidation, setAiValidation] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const token = useSelector(selectAuthToken);

  const contextualHelp = getContextualHelp(contractType, id);

  // AI validation for important fields
  const handleBlur = async () => {
    if (value && ['partyOneName', 'partyTwoName', 'contractTitle'].includes(id) && token) {
      setIsValidating(true);
      try {
        const validation = await validateFieldWithAI(id, value, contractType, 'field_validation', token);
        setAiValidation(validation);
      } catch (error) {
        console.error('AI validation error:', error);
      } finally {
        setIsValidating(false);
      }
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <label htmlFor={id} className="block text-sm font-medium text-slate-300">
          {label}
          {required && <span className="text-red-400 ml-1">*</span>}
        </label>
        {tooltip && (
          <div className="group relative inline-block">
            <FiInfo className="w-4 h-4 text-slate-400 hover:text-blue-400 cursor-help" />
            <div className="invisible group-hover:visible absolute z-10 w-64 p-3 mt-2 text-sm text-white bg-slate-800 border border-slate-600 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-200 left-0">
              {tooltip}
            </div>
          </div>
        )}
      </div>
      
      <div className="relative">
        <input
          type={type}
          id={id}
          name={id}
          placeholder={placeholder}
          value={value || ''}
          onChange={onChange}
          onBlur={handleBlur}
          maxLength={maxLength}
          disabled={disabled}
          className={`
            w-full px-4 py-3 bg-slate-700/50 border rounded-xl text-white placeholder-slate-400 
            transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500
            ${error ? 'border-red-500 bg-red-500/10' : 'border-slate-600 hover:border-slate-500'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${isValidating ? 'border-yellow-500' : ''}
          `}
        />
        
        {isValidating && (
          <div className="absolute top-3 right-3">
            <div className="w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {aiValidation && aiValidation.isValid && (
          <div className="absolute top-3 right-3">
            <FiCheck className="w-4 h-4 text-green-400" />
          </div>
        )}
      </div>

      {/* AI Validation Results */}
      {aiValidation && aiValidation.suggestions.length > 0 && (
        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
          <div className="flex items-center gap-2 text-blue-400 text-sm font-medium mb-2">
            <FiLightbulb className="w-4 h-4" />
            AI Suggestions
          </div>
          <ul className="text-sm text-blue-300 space-y-1">
            {aiValidation.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="w-1 h-1 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {error && (
        <div className="flex items-center gap-2 text-red-400 text-sm">
          <FiAlertTriangle className="w-4 h-4" />
          <span>{error}</span>
        </div>
      )}
      
      {helpText && !error && (
        <p className="text-slate-500 text-sm">{helpText}</p>
      )}
      
      {contextualHelp && !error && !helpText && (
        <p className="text-slate-500 text-sm italic">{contextualHelp}</p>
      )}
      
      {examples && !error && (
        <div className="text-slate-500 text-xs">
          <span className="font-medium">Examples:</span> {examples}
        </div>
      )}
      
      {maxLength && (
        <div className="text-right text-xs text-slate-500">
          {value ? value.length : 0}/{maxLength}
        </div>
      )}
    </div>
  );
};
