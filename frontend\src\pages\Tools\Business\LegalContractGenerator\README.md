# Legal Contract Generator

A comprehensive AI-powered legal contract generation tool that creates professional legal documents with intelligent clause recommendations and compliance guidance.

## Features

### Core Functionality
- **7 Contract Types**: Service Agreements, Partnership Agreements, NDAs, Freelance Contracts, Supplier Agreements, Employment Contracts, and Lease Agreements
- **Multi-Step Form Wizard**: Progressive form with step indicators and validation
- **Real-Time Preview**: Live contract preview that updates as you fill the form
- **AI-Powered Generation**: Intelligent contract creation with legal compliance checks

### Advanced Features
- **Multi-Language Support**: 10+ languages with automatic RTL/LTR text direction detection
- **AI Clause Recommendations**: Smart suggestions based on contract type and context
- **Legal Compliance Guidance**: Jurisdiction-specific legal requirements and suggestions
- **PDF Export**: Professional document export with proper formatting
- **Contract Versioning**: Save and compare different versions of contracts
- **Template Customization**: Save custom templates for reuse
- **Draft Auto-Save**: Automatic saving with 1-hour expiration
- **Error Handling**: Comprehensive error boundary and validation

### Professional Design
- **Legal Industry Styling**: Professional color scheme and typography
- **Responsive Design**: Works on all screen sizes
- **Accessibility**: WCAG compliant with screen reader support
- **Loading States**: Professional loading animations and progress indicators

## Project Structure

```
LegalContractGenerator/
├── LegalContractGenerator.jsx          # Main component
├── ContractTypes/
│   ├── ContractTypeSelection.jsx       # Contract type selection interface
│   └── ContractTypeCard.jsx           # Individual contract type cards
├── FormWizard/
│   ├── FormWizard.jsx                 # Multi-step form controller
│   ├── StepIndicator.jsx              # Progress indicator
│   ├── FormStep.jsx                   # Dynamic step renderer
│   ├── components/
│   │   ├── FormComponents.jsx         # Basic form components
│   │   └── EnhancedFormComponents.jsx # AI-enhanced form components
│   └── steps/                         # Individual form steps
│       ├── BasicInfoStep.jsx
│       ├── ServiceScopeStep.jsx
│       ├── ServiceTermsStep.jsx
│       ├── ServiceLegalStep.jsx
│       ├── PartnershipBasicStep.jsx
│       ├── PartnershipFinancialStep.jsx
│       ├── PartnershipManagementStep.jsx
│       ├── PartnershipLegalStep.jsx
│       ├── NDABasicStep.jsx
│       └── placeholderSteps.js        # Placeholder components
├── PreviewPane/
│   └── PreviewPane.jsx                # Real-time contract preview
├── common/
│   ├── LoadingSteps.jsx               # Loading animation with steps
│   └── ContractResult.jsx             # Final contract display
├── components/
│   └── ErrorBoundary.jsx              # Error handling component
├── hooks/
│   └── useContractState.js            # React hooks for state management
├── utils/
│   ├── contractConstants.js           # Contract types and constants
│   ├── contractTemplates.js           # Template generation utilities
│   ├── formValidation.js              # Form validation rules
│   ├── localStorage.js                # Local storage utilities
│   ├── aiIntegration.js               # AI features and suggestions
│   ├── advancedFeatures.js            # Advanced functionality
│   └── stateManager.js                # State management classes
└── styles/
    └── contractStyles.css             # Professional styling
```

## Backend Structure

```
backend/javascript/
├── controllers/tools/BusinessPlan/
│   ├── legalContractController.js     # Main API controller
│   └── legalContractPrompts.js        # AI prompt engineering
└── routes/Tools/Business/
    └── legalContractRoutes.js         # API routes
```

## API Endpoints

### Contract Generation
- `POST /api/legal-contracts/generate` - Generate a legal contract
- `GET /api/legal-contracts/stats` - Get user contract statistics

### Request Format
```json
{
  "contractType": "service",
  "language": "English",
  "jurisdiction": "United States",
  "partyOneName": "Service Provider Name",
  "partyTwoName": "Client Name",
  "effectiveDate": "2024-01-01",
  "scopeOfWork": "Detailed description...",
  "paymentAmount": 5000,
  // ... other contract-specific fields
}
```

### Response Format
```json
{
  "success": true,
  "contract": "Generated contract content...",
  "contractType": "service",
  "metadata": {
    "language": "English",
    "jurisdiction": "United States",
    "generatedAt": "2024-01-01T00:00:00.000Z"
  },
  "subscription": {
    "plan": "pro",
    "freeTierLegalContractCount": 0,
    "proTierLegalContractCount": 1
  }
}
```

## Usage Limits

- **Free Tier**: 2 contracts per month
- **Pro Tier**: 25 contracts per month
- **Starter Tier**: 10 contracts per month

## Contract Types

### 1. Service Agreement
- Scope of work definition
- Payment terms and schedules
- Deliverables and timelines
- Intellectual property rights
- Termination clauses

### 2. Partnership Agreement
- Profit and loss sharing
- Decision-making authority
- Capital contributions
- Exit strategies
- Dispute resolution

### 3. Non-Disclosure Agreement (NDA)
- Confidentiality obligations
- Permitted disclosures
- Duration of confidentiality
- Return of materials
- Legal remedies

### 4. Freelance/Contractor Agreement
- Independent contractor status
- Project specifications
- Payment milestones
- Work ownership rights
- Performance standards

### 5. Supplier/Vendor Agreement
- Supply specifications
- Quality standards
- Delivery terms
- Pricing and payment
- Performance guarantees

### 6. Employment Contract
- Job responsibilities
- Compensation and benefits
- Working conditions
- Confidentiality terms
- Termination procedures

### 7. Lease Agreement
- Property description
- Rent and deposit terms
- Lease duration
- Maintenance responsibilities
- Renewal options

## AI Features

### Intelligent Clause Recommendations
- Context-aware suggestions based on contract type
- Industry best practices integration
- Risk assessment and mitigation suggestions

### Legal Compliance Guidance
- Jurisdiction-specific requirements
- Regulatory compliance checks
- Standard legal clause recommendations

### Smart Form Validation
- Real-time field validation
- AI-powered content suggestions
- Contextual help and examples

## Multi-Language Support

Supported languages with proper text direction:
- English (LTR)
- Spanish (LTR)
- French (LTR)
- German (LTR)
- Italian (LTR)
- Portuguese (LTR)
- Arabic (RTL)
- Chinese (LTR)
- Japanese (LTR)
- Korean (LTR)

## State Management

The application uses a sophisticated state management system with:
- Auto-save functionality (30-second intervals)
- Form history for undo operations
- Local storage with auto-expiration
- Comprehensive error handling
- Validation state management

## Styling and Design

### Color Palette
- **Primary**: Professional blue (#1e40af)
- **Secondary**: Dark gray (#374151)
- **Accent**: Success green (#059669)
- **Warning**: Amber (#d97706)
- **Danger**: Red (#dc2626)

### Typography
- **Headings**: Georgia, Times New Roman (serif)
- **Body**: Inter, Segoe UI (sans-serif)
- **Documents**: Georgia, Times New Roman (serif)

### Responsive Breakpoints
- Mobile: < 640px
- Tablet: 640px - 768px
- Desktop: > 768px

## Accessibility

- WCAG 2.1 AA compliant
- Screen reader support
- Keyboard navigation
- High contrast mode support
- Reduced motion support
- Focus management

## Error Handling

- Comprehensive error boundary
- User-friendly error messages
- Error reporting system
- Graceful degradation
- Recovery mechanisms

## Legal Disclaimer

The contracts generated by this tool are templates designed to provide a starting point for legal agreements. While the AI incorporates industry best practices and legal standards, we strongly recommend having any contract reviewed by a qualified attorney before execution. This tool does not constitute legal advice.

## Development

### Prerequisites
- React 18+
- Redux Toolkit
- Tailwind CSS
- Node.js backend with Express

### Installation
1. Install dependencies
2. Configure environment variables
3. Set up backend API endpoints
4. Configure AI service integration

### Testing
- Unit tests for components
- Integration tests for API endpoints
- E2E tests for user workflows
- Accessibility testing

## Contributing

1. Follow the established component structure
2. Maintain consistent styling patterns
3. Add proper error handling
4. Include accessibility features
5. Write comprehensive tests
6. Update documentation
