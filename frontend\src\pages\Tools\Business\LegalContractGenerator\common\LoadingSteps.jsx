import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiFileText, FiShield, FiTarget, FiZap } from 'react-icons/fi';
import { CONTRACT_TYPES } from '../utils/contractConstants';

const LoadingSteps = ({ contractType }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const contractTypeInfo = CONTRACT_TYPES[contractType];

  const steps = [
    {
      id: 'analyzing',
      title: 'Analyzing Your Requirements',
      description: 'Processing contract details and requirements',
      icon: FiTarget,
      duration: 2000
    },
    {
      id: 'generating',
      title: 'Generating Legal Clauses',
      description: 'Creating professional contract clauses with AI',
      icon: FiFileText,
      duration: 3000
    },
    {
      id: 'compliance',
      title: 'Ensuring Legal Compliance',
      description: 'Verifying compliance with legal standards',
      icon: FiShield,
      duration: 2500
    },
    {
      id: 'finalizing',
      title: 'Finalizing Your Contract',
      description: 'Applying final formatting and review',
      icon: FiZap,
      duration: 1500
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) {
          return prev + 1;
        }
        return prev;
      });
    }, 2200);

    return () => clearInterval(timer);
  }, [steps.length]);

  return (
    <div className="w-full max-w-4xl mx-auto px-4">
      {/* Header */}
      <div className="text-center mb-12">
        <div className={`inline-flex items-center justify-center p-4 ${contractTypeInfo.bgColor} ${contractTypeInfo.borderColor} border rounded-full mb-6 backdrop-blur-sm animate-pulse`}>
          <contractTypeInfo.icon className={`w-8 h-8 ${contractTypeInfo.textColor} mr-3`} />
          <span className={`${contractTypeInfo.textColor} font-semibold text-lg`}>
            Generating {contractTypeInfo.title}
          </span>
        </div>
        <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-4">
          Creating Your Contract
        </h2>
        <p className="text-slate-400 text-lg max-w-2xl mx-auto leading-relaxed">
          Our AI is crafting a professional legal contract tailored to your specific requirements. 
          This process ensures compliance with legal standards and industry best practices.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="space-y-6 mb-12">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          const isPending = index > currentStep;
          const StepIcon = step.icon;

          return (
            <div
              key={step.id}
              className={`
                flex items-center gap-6 p-6 rounded-2xl border transition-all duration-500
                ${isCompleted 
                  ? 'bg-green-500/10 border-green-500/30' 
                  : isCurrent 
                    ? 'bg-blue-500/10 border-blue-500/30 shadow-lg shadow-blue-500/20' 
                    : 'bg-slate-800/50 border-slate-700'
                }
              `}
            >
              {/* Step Icon */}
              <div
                className={`
                  w-16 h-16 rounded-full border-2 flex items-center justify-center transition-all duration-500
                  ${isCompleted 
                    ? 'bg-green-500/20 border-green-500 text-green-400' 
                    : isCurrent 
                      ? 'bg-blue-500/20 border-blue-500 text-blue-400' 
                      : 'bg-slate-700 border-slate-600 text-slate-400'
                  }
                `}
              >
                {isCompleted ? (
                  <FiCheck className="w-8 h-8 animate-scale-in" />
                ) : isCurrent ? (
                  <FiLoader className="w-8 h-8 animate-spin" />
                ) : (
                  <StepIcon className="w-8 h-8" />
                )}
              </div>

              {/* Step Content */}
              <div className="flex-1">
                <h3
                  className={`
                    text-xl font-semibold mb-2 transition-colors duration-500
                    ${isCompleted 
                      ? 'text-green-400' 
                      : isCurrent 
                        ? 'text-blue-400' 
                        : 'text-slate-400'
                    }
                  `}
                >
                  {step.title}
                </h3>
                <p
                  className={`
                    transition-colors duration-500
                    ${isCompleted 
                      ? 'text-green-300/80' 
                      : isCurrent 
                        ? 'text-blue-300/80' 
                        : 'text-slate-500'
                    }
                  `}
                >
                  {step.description}
                </p>
              </div>

              {/* Status Indicator */}
              <div className="flex items-center">
                {isCompleted && (
                  <span className="text-green-400 text-sm font-medium animate-fade-in">
                    Complete
                  </span>
                )}
                {isCurrent && (
                  <div className="flex items-center gap-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-blue-400 text-sm font-medium ml-2">
                      Processing...
                    </span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <span className="text-slate-400 text-sm">Overall Progress</span>
          <span className="text-slate-400 text-sm">
            {Math.round(((currentStep + 1) / steps.length) * 100)}%
          </span>
        </div>
        <div className="w-full bg-slate-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* AI Processing Info */}
      <div className="bg-slate-900/50 border border-slate-700 rounded-xl p-6 text-center">
        <div className="flex items-center justify-center gap-3 mb-3">
          <FiZap className="w-5 h-5 text-yellow-400" />
          <span className="text-yellow-400 font-semibold">AI-Powered Generation</span>
        </div>
        <p className="text-slate-400 text-sm leading-relaxed">
          Our advanced AI is analyzing your requirements and generating a comprehensive legal contract 
          with industry-standard clauses, compliance checks, and professional formatting. This ensures 
          your contract meets legal standards while being tailored to your specific needs.
        </p>
      </div>
    </div>
  );
};

export default LoadingSteps;
