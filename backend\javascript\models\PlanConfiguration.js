// models/PlanConfiguration.js
import mongoose from 'mongoose';

// Schema for individual plan limits
const planLimitsSchema = new mongoose.Schema({
    pdfUpload: { type: Number, default: 0, min: 0 },
    businessPlan: { type: Number, default: 0, min: 0 },
    investorPitch: { type: Number, default: 0, min: 0 },
    businessQA: { type: Number, default: 0, min: 0 },
    legalContracts: { type: Number, default: 0, min: 0 },
    message: { type: Number, default: 0, min: 0 }
}, { _id: false });

// Main plan configuration schema
const planConfigurationSchema = new mongoose.Schema({
    planName: {
        type: String,
        required: true,
        unique: true,
        enum: ['Starter', 'Pro']
    },
    displayName: {
        type: String,
        required: true
    },
    limits: {
        type: planLimitsSchema,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    }
}, { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Default configuration values (matching current planConfig.js)
const defaultConfigurations = {
    Starter: {
        planName: 'Starter',
        displayName: 'Starter Plan',
        limits: {
            pdfUpload: 5,
            businessPlan: 1,
            investorPitch: 3,
            businessQA: 5,
            legalContracts: 2,
            message: 100
        },
        isActive: true
    },
    Pro: {
        planName: 'Pro',
        displayName: 'Pro Plan',
        limits: {
            pdfUpload: 25,
            businessPlan: 20,
            investorPitch: 25,
            businessQA: 50,
            legalContracts: 25,
            message: 300
        },
        isActive: true
    }
};

// Static method to initialize default configurations
planConfigurationSchema.statics.initializeDefaults = async function() {
    console.log('Initializing default plan configurations...');
    
    for (const [planName, config] of Object.entries(defaultConfigurations)) {
        const existingConfig = await this.findOne({ planName });
        
        if (!existingConfig) {
            await this.create(config);
            console.log(`Created default configuration for ${planName} plan`);
        } else {
            console.log(`Configuration for ${planName} plan already exists`);
        }
    }
};

// Static method to get configuration for a specific plan
planConfigurationSchema.statics.getPlanConfig = async function(planName) {
    const config = await this.findOne({ planName, isActive: true });
    return config;
};

// Static method to get all active configurations
planConfigurationSchema.statics.getAllConfigs = async function() {
    const configs = await this.find({ isActive: true }).sort({ planName: 1 });
    return configs;
};

// Static method to update plan configuration
planConfigurationSchema.statics.updatePlanConfig = async function(planName, limits, updatedBy = null) {
    const config = await this.findOneAndUpdate(
        { planName, isActive: true },
        { 
            limits,
            lastUpdated: new Date(),
            updatedBy
        },
        { new: true, runValidators: true }
    );
    
    if (!config) {
        throw new Error(`Plan configuration for ${planName} not found`);
    }
    
    return config;
};

// Static method to get limit for a specific plan and feature (backward compatibility)
planConfigurationSchema.statics.getLimit = async function(planName, feature) {
    const config = await this.getPlanConfig(planName);
    
    if (!config) {
        console.warn(`Plan configuration for ${planName} not found, returning 0`);
        return 0;
    }
    
    return config.limits[feature] || 0;
};

// Instance method to update limits
planConfigurationSchema.methods.updateLimits = function(newLimits, updatedBy = null) {
    this.limits = { ...this.limits.toObject(), ...newLimits };
    this.lastUpdated = new Date();
    this.updatedBy = updatedBy;
    return this.save();
};

// Pre-save middleware to update lastUpdated
planConfigurationSchema.pre('save', function(next) {
    if (this.isModified('limits')) {
        this.lastUpdated = new Date();
    }
    next();
});

// Virtual for formatted last updated
planConfigurationSchema.virtual('formattedLastUpdated').get(function() {
    return this.lastUpdated ? this.lastUpdated.toISOString() : null;
});

const PlanConfiguration = mongoose.model('PlanConfiguration', planConfigurationSchema);

export default PlanConfiguration;
