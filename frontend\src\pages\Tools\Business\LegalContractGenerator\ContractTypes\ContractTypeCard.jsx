import React from 'react';
import { FiCheck, FiArrowRight } from 'react-icons/fi';

const ContractTypeCard = ({ 
  contractType, 
  isSelected, 
  isHovered, 
  onSelect, 
  onHover, 
  delay = 0 
}) => {
  const IconComponent = contractType.icon;

  const handleClick = () => {
    onSelect(contractType.id);
  };

  const handleMouseEnter = () => {
    onHover(contractType.id);
  };

  const handleMouseLeave = () => {
    onHover(null);
  };

  return (
    <div
      className={`
        relative group cursor-pointer transform transition-all duration-500 hover:scale-105
        animate-fade-in
      `}
      style={{ animationDelay: `${delay}ms` }}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Main Card */}
      <div
        className={`
          relative p-6 rounded-2xl border-2 transition-all duration-300
          ${isSelected 
            ? `${contractType.borderColor} ${contractType.bgColor} shadow-xl shadow-${contractType.textColor.split('-')[1]}-500/20` 
            : 'border-slate-700 bg-slate-800/50 hover:border-slate-600'
          }
          ${isHovered && !isSelected ? 'border-slate-600 bg-slate-800/70' : ''}
        `}
      >
        {/* Selection Indicator */}
        {isSelected && (
          <div className={`absolute -top-2 -right-2 w-6 h-6 ${contractType.bgColor} ${contractType.borderColor} border-2 rounded-full flex items-center justify-center animate-scale-in`}>
            <FiCheck className={`w-3 h-3 ${contractType.textColor}`} />
          </div>
        )}

        {/* Icon */}
        <div className={`
          w-16 h-16 rounded-xl ${contractType.bgColor} ${contractType.borderColor} border 
          flex items-center justify-center mb-4 transition-all duration-300
          ${isSelected ? 'scale-110' : 'group-hover:scale-105'}
        `}>
          <IconComponent className={`w-8 h-8 ${contractType.textColor}`} />
        </div>

        {/* Title */}
        <h3 className={`
          text-xl font-bold mb-3 transition-colors duration-300
          ${isSelected ? contractType.textColor : 'text-white group-hover:text-slate-200'}
        `}>
          {contractType.title}
        </h3>

        {/* Description */}
        <p className="text-slate-400 text-sm leading-relaxed mb-4 group-hover:text-slate-300 transition-colors duration-300">
          {contractType.description}
        </p>

        {/* Features List */}
        <div className="space-y-2">
          {contractType.features.slice(0, 3).map((feature, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className={`w-1.5 h-1.5 rounded-full ${isSelected ? contractType.textColor.replace('text-', 'bg-') : 'bg-slate-500'} transition-colors duration-300`}></div>
              <span className="text-slate-400 text-xs group-hover:text-slate-300 transition-colors duration-300">
                {feature}
              </span>
            </div>
          ))}
          {contractType.features.length > 3 && (
            <div className="flex items-center gap-2">
              <div className={`w-1.5 h-1.5 rounded-full ${isSelected ? contractType.textColor.replace('text-', 'bg-') : 'bg-slate-500'} transition-colors duration-300`}></div>
              <span className="text-slate-400 text-xs group-hover:text-slate-300 transition-colors duration-300">
                +{contractType.features.length - 3} more features
              </span>
            </div>
          )}
        </div>

        {/* Hover Arrow */}
        <div className={`
          absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300
          ${isSelected ? 'opacity-100' : ''}
        `}>
          <FiArrowRight className={`w-5 h-5 ${isSelected ? contractType.textColor : 'text-slate-400'} transition-colors duration-300`} />
        </div>

        {/* Gradient Overlay on Hover */}
        <div className={`
          absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none
          bg-gradient-to-br ${contractType.color}
          ${isSelected ? 'opacity-10' : ''}
        `}></div>
      </div>

      {/* Glow Effect for Selected */}
      {isSelected && (
        <div className={`
          absolute inset-0 rounded-2xl blur-xl opacity-20 -z-10
          bg-gradient-to-br ${contractType.color}
          animate-pulse
        `}></div>
      )}
    </div>
  );
};

export default ContractTypeCard;
