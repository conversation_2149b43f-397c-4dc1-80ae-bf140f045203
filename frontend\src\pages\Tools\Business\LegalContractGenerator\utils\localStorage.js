// Local storage utilities for Legal Contract Generator
// Auto-expiration set to 1 hour as per user preferences

const STORAGE_EXPIRY_TIME = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Save data to localStorage with timestamp for auto-expiration
 * @param {string} key - Storage key
 * @param {any} data - Data to store
 */
export const saveToLocalStorage = (key, data) => {
  try {
    const storageData = {
      data: data,
      timestamp: Date.now(),
      expiresAt: Date.now() + STORAGE_EXPIRY_TIME
    };
    localStorage.setItem(key, JSON.stringify(storageData));
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

/**
 * Load data from localStorage with expiration check
 * @param {string} key - Storage key
 * @returns {any|null} - Stored data or null if expired/not found
 */
export const loadFromLocalStorage = (key) => {
  try {
    const stored = localStorage.getItem(key);
    if (!stored) return null;

    const storageData = JSON.parse(stored);
    const now = Date.now();

    // Check if data has expired
    if (storageData.expiresAt && now > storageData.expiresAt) {
      localStorage.removeItem(key);
      return null;
    }

    return storageData.data;
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
    return null;
  }
};

/**
 * Clear specific item from localStorage
 * @param {string} key - Storage key
 */
export const clearLocalStorage = (key) => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.warn('Failed to clear localStorage:', error);
  }
};

/**
 * Clear all expired items from localStorage
 */
export const clearExpiredStorage = () => {
  try {
    const now = Date.now();
    const keysToRemove = [];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('legalContract')) {
        try {
          const stored = localStorage.getItem(key);
          const storageData = JSON.parse(stored);
          
          if (storageData.expiresAt && now > storageData.expiresAt) {
            keysToRemove.push(key);
          }
        } catch (error) {
          // If we can't parse it, it's probably corrupted, so remove it
          keysToRemove.push(key);
        }
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
  } catch (error) {
    console.warn('Failed to clear expired storage:', error);
  }
};

/**
 * Get remaining time before expiration
 * @param {string} key - Storage key
 * @returns {number} - Remaining time in milliseconds, or 0 if expired/not found
 */
export const getRemainingTime = (key) => {
  try {
    const stored = localStorage.getItem(key);
    if (!stored) return 0;

    const storageData = JSON.parse(stored);
    const now = Date.now();

    if (storageData.expiresAt && now < storageData.expiresAt) {
      return storageData.expiresAt - now;
    }

    return 0;
  } catch (error) {
    console.warn('Failed to get remaining time:', error);
    return 0;
  }
};

/**
 * Check if stored data exists and is not expired
 * @param {string} key - Storage key
 * @returns {boolean} - True if data exists and is valid
 */
export const hasValidStorage = (key) => {
  return loadFromLocalStorage(key) !== null;
};

// Auto-cleanup expired storage on module load
clearExpiredStorage();
