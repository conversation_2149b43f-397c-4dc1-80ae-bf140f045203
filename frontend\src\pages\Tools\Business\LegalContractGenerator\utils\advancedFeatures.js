// Advanced features for Legal Contract Generator

import { LANGUAGES } from './contractConstants';

/**
 * Detect text direction based on language
 */
export const detectTextDirection = (language) => {
  const rtlLanguages = ['Arabic', 'Hebrew', 'Persian', 'Urdu'];
  return rtlLanguages.includes(language) ? 'rtl' : 'ltr';
};

/**
 * Get language-specific formatting rules
 */
export const getLanguageFormatting = (language) => {
  const languageConfig = LANGUAGES.find(lang => lang.code === language);
  
  return {
    direction: languageConfig?.direction || 'ltr',
    fontFamily: getLanguageFont(language),
    textAlign: languageConfig?.direction === 'rtl' ? 'right' : 'left',
    lineHeight: getLanguageLineHeight(language)
  };
};

/**
 * Get appropriate font for language
 */
const getLanguageFont = (language) => {
  const fontMap = {
    'Arabic': 'Noto Sans Arabic, Arial, sans-serif',
    'Chinese': 'Noto Sans SC, SimSun, sans-serif',
    'Japanese': 'Noto Sans JP, Yu Gothic, sans-serif',
    'Korean': 'Noto Sans KR, Malgun Gothic, sans-serif',
    'Hebrew': 'Noto Sans Hebrew, Arial Hebrew, sans-serif',
    'default': 'Georgia, Times New Roman, serif'
  };
  
  return fontMap[language] || fontMap.default;
};

/**
 * Get appropriate line height for language
 */
const getLanguageLineHeight = (language) => {
  const lineHeightMap = {
    'Chinese': '1.9',
    'Japanese': '1.9',
    'Korean': '1.9',
    'Arabic': '1.8',
    'default': '1.7'
  };
  
  return lineHeightMap[language] || lineHeightMap.default;
};

/**
 * Contract versioning utilities
 */
export class ContractVersionManager {
  constructor() {
    this.versions = this.loadVersions();
  }

  loadVersions() {
    try {
      const stored = localStorage.getItem('contractVersions');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error loading contract versions:', error);
      return {};
    }
  }

  saveVersions() {
    try {
      localStorage.setItem('contractVersions', JSON.stringify(this.versions));
    } catch (error) {
      console.error('Error saving contract versions:', error);
    }
  }

  saveVersion(contractId, versionData) {
    if (!this.versions[contractId]) {
      this.versions[contractId] = [];
    }

    const version = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      formData: versionData.formData,
      contract: versionData.contract,
      contractType: versionData.contractType,
      version: this.versions[contractId].length + 1,
      notes: versionData.notes || ''
    };

    this.versions[contractId].push(version);
    this.saveVersions();
    
    return version;
  }

  getVersions(contractId) {
    return this.versions[contractId] || [];
  }

  getVersion(contractId, versionId) {
    const versions = this.getVersions(contractId);
    return versions.find(v => v.id === versionId);
  }

  deleteVersion(contractId, versionId) {
    if (this.versions[contractId]) {
      this.versions[contractId] = this.versions[contractId].filter(v => v.id !== versionId);
      this.saveVersions();
    }
  }

  compareVersions(contractId, versionId1, versionId2) {
    const version1 = this.getVersion(contractId, versionId1);
    const version2 = this.getVersion(contractId, versionId2);
    
    if (!version1 || !version2) {
      return null;
    }

    return {
      version1,
      version2,
      differences: this.findDifferences(version1.formData, version2.formData)
    };
  }

  findDifferences(data1, data2) {
    const differences = [];
    const allKeys = new Set([...Object.keys(data1), ...Object.keys(data2)]);

    allKeys.forEach(key => {
      if (data1[key] !== data2[key]) {
        differences.push({
          field: key,
          oldValue: data1[key] || '',
          newValue: data2[key] || ''
        });
      }
    });

    return differences;
  }
}

/**
 * Template customization utilities
 */
export class TemplateCustomizer {
  constructor() {
    this.customTemplates = this.loadCustomTemplates();
  }

  loadCustomTemplates() {
    try {
      const stored = localStorage.getItem('customContractTemplates');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Error loading custom templates:', error);
      return {};
    }
  }

  saveCustomTemplates() {
    try {
      localStorage.setItem('customContractTemplates', JSON.stringify(this.customTemplates));
    } catch (error) {
      console.error('Error saving custom templates:', error);
    }
  }

  saveTemplate(templateData) {
    const template = {
      id: Date.now().toString(),
      name: templateData.name,
      contractType: templateData.contractType,
      formData: templateData.formData,
      customClauses: templateData.customClauses || [],
      createdAt: new Date().toISOString(),
      description: templateData.description || ''
    };

    if (!this.customTemplates[templateData.contractType]) {
      this.customTemplates[templateData.contractType] = [];
    }

    this.customTemplates[templateData.contractType].push(template);
    this.saveCustomTemplates();
    
    return template;
  }

  getTemplates(contractType) {
    return this.customTemplates[contractType] || [];
  }

  getTemplate(contractType, templateId) {
    const templates = this.getTemplates(contractType);
    return templates.find(t => t.id === templateId);
  }

  deleteTemplate(contractType, templateId) {
    if (this.customTemplates[contractType]) {
      this.customTemplates[contractType] = this.customTemplates[contractType].filter(t => t.id !== templateId);
      this.saveCustomTemplates();
    }
  }

  updateTemplate(contractType, templateId, updates) {
    const templates = this.getTemplates(contractType);
    const templateIndex = templates.findIndex(t => t.id === templateId);
    
    if (templateIndex !== -1) {
      templates[templateIndex] = { ...templates[templateIndex], ...updates, updatedAt: new Date().toISOString() };
      this.saveCustomTemplates();
      return templates[templateIndex];
    }
    
    return null;
  }
}

/**
 * PDF export utilities
 */
export const exportToPDF = async (contractContent, contractTitle, options = {}) => {
  try {
    // This would integrate with a PDF library like jsPDF or PDFKit
    // For now, we'll create a simple implementation
    
    const {
      fontSize = 12,
      fontFamily = 'Times',
      margin = 40,
      language = 'English'
    } = options;

    const formatting = getLanguageFormatting(language);
    
    // Create a blob with formatted content for download
    const htmlContent = `
      <!DOCTYPE html>
      <html dir="${formatting.direction}">
        <head>
          <meta charset="UTF-8">
          <title>${contractTitle}</title>
          <style>
            body {
              font-family: ${formatting.fontFamily};
              font-size: ${fontSize}px;
              line-height: ${formatting.lineHeight};
              margin: ${margin}px;
              text-align: ${formatting.textAlign};
              direction: ${formatting.direction};
            }
            h1, h2, h3 {
              color: #1e293b;
              margin-top: 30px;
              margin-bottom: 15px;
            }
            h1 {
              text-align: center;
              font-size: 24px;
              margin-bottom: 30px;
            }
            p {
              margin-bottom: 15px;
            }
            .signature-block {
              margin-top: 50px;
              page-break-inside: avoid;
            }
            .page-break {
              page-break-before: always;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          ${contractContent}
          
          <div class="signature-block">
            <table width="100%" style="margin-top: 50px;">
              <tr>
                <td width="45%" style="border-bottom: 1px solid #000; padding-bottom: 5px;">
                  <br><br><br>
                </td>
                <td width="10%"></td>
                <td width="45%" style="border-bottom: 1px solid #000; padding-bottom: 5px;">
                  <br><br><br>
                </td>
              </tr>
              <tr>
                <td style="padding-top: 10px; text-align: center;">
                  <strong>Party 1 Signature</strong><br>
                  Date: _______________
                </td>
                <td></td>
                <td style="padding-top: 10px; text-align: center;">
                  <strong>Party 2 Signature</strong><br>
                  Date: _______________
                </td>
              </tr>
            </table>
          </div>
        </body>
      </html>
    `;

    // Create blob and download
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${contractTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    return { success: true, message: 'Contract exported successfully' };
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return { success: false, error: 'Failed to export contract' };
  }
};

/**
 * Contract sharing utilities
 */
export const shareContract = async (contractData, method = 'link') => {
  try {
    switch (method) {
      case 'link':
        // Generate a shareable link (would require backend implementation)
        const shareId = Date.now().toString();
        const shareUrl = `${window.location.origin}/contract/shared/${shareId}`;
        
        if (navigator.share) {
          await navigator.share({
            title: contractData.title,
            text: 'Legal Contract',
            url: shareUrl
          });
        } else {
          await navigator.clipboard.writeText(shareUrl);
          return { success: true, message: 'Share link copied to clipboard' };
        }
        break;
        
      case 'email':
        const emailSubject = encodeURIComponent(`Legal Contract: ${contractData.title}`);
        const emailBody = encodeURIComponent(`Please find the legal contract attached.\n\nContract: ${contractData.title}\nGenerated: ${new Date().toLocaleDateString()}`);
        window.open(`mailto:?subject=${emailSubject}&body=${emailBody}`);
        break;
        
      default:
        throw new Error('Unsupported sharing method');
    }
    
    return { success: true, message: 'Contract shared successfully' };
  } catch (error) {
    console.error('Error sharing contract:', error);
    return { success: false, error: 'Failed to share contract' };
  }
};

/**
 * Contract analytics and insights
 */
export const getContractInsights = (formData, contractType) => {
  const insights = [];

  // Analyze contract complexity
  const textFields = Object.values(formData).filter(value => 
    typeof value === 'string' && value.length > 50
  );
  
  const totalWords = textFields.reduce((count, text) => 
    count + text.split(' ').length, 0
  );

  if (totalWords < 100) {
    insights.push({
      type: 'warning',
      title: 'Contract Brevity',
      message: 'Your contract is quite brief. Consider adding more detail to prevent misunderstandings.',
      suggestion: 'Add more specific terms and conditions to protect both parties.'
    });
  } else if (totalWords > 1000) {
    insights.push({
      type: 'info',
      title: 'Comprehensive Contract',
      message: 'Your contract is very detailed, which is good for clarity.',
      suggestion: 'Ensure all parties can easily understand the terms.'
    });
  }

  // Contract-specific insights
  if (contractType === 'service') {
    if (!formData.intellectualProperty) {
      insights.push({
        type: 'warning',
        title: 'Missing IP Terms',
        message: 'Consider adding intellectual property clauses.',
        suggestion: 'Define who owns the work product and any pre-existing materials.'
      });
    }
  }

  if (contractType === 'employment') {
    if (!formData.benefits) {
      insights.push({
        type: 'info',
        title: 'Benefits Package',
        message: 'Consider specifying employee benefits.',
        suggestion: 'Include health insurance, vacation time, and other benefits.'
      });
    }
  }

  return insights;
};
