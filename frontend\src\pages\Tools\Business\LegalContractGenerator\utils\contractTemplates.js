// Contract template generation utilities

/**
 * Generate preview content for a contract based on form data
 */
export const generatePreviewContent = (contractType, formData) => {
  if (!contractType || !formData || Object.keys(formData).length === 0) {
    return null;
  }

  const generators = {
    service: generateServiceAgreementPreview,
    partnership: generatePartnershipAgreementPreview,
    nda: generateNDAPreview,
    freelance: generateFreelanceAgreementPreview,
    supplier: generateSupplierAgreementPreview,
    employment: generateEmploymentContractPreview,
    lease: generateLeaseAgreementPreview
  };

  const generator = generators[contractType];
  return generator ? generator(formData) : generateGenericPreview(formData);
};

/**
 * Service Agreement Preview Generator
 */
const generateServiceAgreementPreview = (formData) => {
  const {
    contractTitle = 'Service Agreement',
    partyOneName = '[Service Provider]',
    partyTwoName = '[Client]',
    effectiveDate = '[Date]',
    scopeOfWork = '[Scope of work to be defined]',
    deliverables = '[Deliverables to be specified]',
    paymentAmount = '[Amount]',
    paymentSchedule = '[Schedule]'
  } = formData;

  return `
    <div class="contract-content">
      <h1 style="text-align: center; margin-bottom: 30px; color: #1e293b; font-size: 24px;">
        ${contractTitle}
      </h1>
      
      <p style="margin-bottom: 20px;">
        This Service Agreement ("Agreement") is entered into on <strong>${effectiveDate}</strong> 
        between <strong>${partyOneName}</strong> ("Service Provider") and <strong>${partyTwoName}</strong> ("Client").
      </p>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">1. SCOPE OF WORK</h2>
      <p style="margin-bottom: 15px;">
        The Service Provider agrees to provide the following services:
      </p>
      <div style="background: #f8fafc; padding: 15px; border-left: 4px solid #3b82f6; margin-bottom: 20px;">
        ${scopeOfWork || 'Detailed scope of work will be specified here based on your requirements.'}
      </div>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">2. DELIVERABLES</h2>
      <div style="background: #f8fafc; padding: 15px; border-left: 4px solid #10b981; margin-bottom: 20px;">
        ${deliverables || 'Specific deliverables and outputs will be listed here.'}
      </div>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">3. PAYMENT TERMS</h2>
      <p style="margin-bottom: 15px;">
        <strong>Total Contract Value:</strong> ${paymentAmount ? `$${paymentAmount}` : '[Amount to be specified]'}
      </p>
      <p style="margin-bottom: 15px;">
        <strong>Payment Schedule:</strong> ${paymentSchedule || '[Payment schedule to be defined]'}
      </p>

      ${formData.intellectualProperty ? `
        <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">4. INTELLECTUAL PROPERTY</h2>
        <div style="background: #fef3c7; padding: 15px; border-left: 4px solid #f59e0b; margin-bottom: 20px;">
          ${formData.intellectualProperty}
        </div>
      ` : ''}

      <div style="margin-top: 40px; padding: 20px; background: #f1f5f9; border-radius: 8px;">
        <p style="margin: 0; font-style: italic; color: #64748b; text-align: center;">
          This is a preview of your contract. Complete all sections to generate the full legal document.
        </p>
      </div>
    </div>
  `;
};

/**
 * Partnership Agreement Preview Generator
 */
const generatePartnershipAgreementPreview = (formData) => {
  const {
    contractTitle = 'Partnership Agreement',
    businessName = '[Partnership Name]',
    partyOneName = '[Partner 1]',
    partyTwoName = '[Partner 2]',
    businessPurpose = '[Business purpose to be defined]',
    capitalContribution = '[Amount]'
  } = formData;

  return `
    <div class="contract-content">
      <h1 style="text-align: center; margin-bottom: 30px; color: #1e293b; font-size: 24px;">
        ${contractTitle}
      </h1>
      
      <p style="margin-bottom: 20px;">
        This Partnership Agreement establishes <strong>${businessName}</strong> as a partnership 
        between <strong>${partyOneName}</strong> and <strong>${partyTwoName}</strong>.
      </p>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">1. BUSINESS PURPOSE</h2>
      <div style="background: #f8fafc; padding: 15px; border-left: 4px solid #8b5cf6; margin-bottom: 20px;">
        ${businessPurpose || 'The purpose and scope of the partnership business will be defined here.'}
      </div>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">2. CAPITAL CONTRIBUTIONS</h2>
      <p style="margin-bottom: 15px;">
        Initial capital contribution: ${capitalContribution ? `$${capitalContribution}` : '[Amount to be specified]'}
      </p>

      <div style="margin-top: 40px; padding: 20px; background: #f1f5f9; border-radius: 8px;">
        <p style="margin: 0; font-style: italic; color: #64748b; text-align: center;">
          Partnership agreement preview - complete all sections for full document.
        </p>
      </div>
    </div>
  `;
};

/**
 * NDA Preview Generator
 */
const generateNDAPreview = (formData) => {
  const {
    contractTitle = 'Non-Disclosure Agreement',
    disclosingParty = '[Disclosing Party]',
    receivingParty = '[Receiving Party]',
    purpose = '[Purpose to be defined]',
    confidentialInfo = '[Confidential information definition]'
  } = formData;

  return `
    <div class="contract-content">
      <h1 style="text-align: center; margin-bottom: 30px; color: #1e293b; font-size: 24px;">
        ${contractTitle}
      </h1>
      
      <p style="margin-bottom: 20px;">
        This Non-Disclosure Agreement is between <strong>${disclosingParty}</strong> 
        and <strong>${receivingParty}</strong> for the purpose of protecting confidential information.
      </p>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">1. PURPOSE</h2>
      <div style="background: #fef2f2; padding: 15px; border-left: 4px solid #ef4444; margin-bottom: 20px;">
        ${purpose || 'The purpose of this confidentiality agreement will be specified here.'}
      </div>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">2. CONFIDENTIAL INFORMATION</h2>
      <div style="background: #f8fafc; padding: 15px; border-left: 4px solid #6366f1; margin-bottom: 20px;">
        ${confidentialInfo || 'Definition of what constitutes confidential information will be detailed here.'}
      </div>

      <div style="margin-top: 40px; padding: 20px; background: #f1f5f9; border-radius: 8px;">
        <p style="margin: 0; font-style: italic; color: #64748b; text-align: center;">
          NDA preview - complete all sections for comprehensive confidentiality protection.
        </p>
      </div>
    </div>
  `;
};

/**
 * Freelance Agreement Preview Generator
 */
const generateFreelanceAgreementPreview = (formData) => {
  const {
    contractTitle = 'Freelance Agreement',
    contractorName = '[Contractor]',
    clientName = '[Client]',
    projectTitle = '[Project Title]',
    workDescription = '[Work description]',
    paymentRate = '[Rate]'
  } = formData;

  return `
    <div class="contract-content">
      <h1 style="text-align: center; margin-bottom: 30px; color: #1e293b; font-size: 24px;">
        ${contractTitle}
      </h1>
      
      <p style="margin-bottom: 20px;">
        This Freelance Agreement is for <strong>${projectTitle}</strong> between 
        <strong>${contractorName}</strong> (Contractor) and <strong>${clientName}</strong> (Client).
      </p>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">1. PROJECT DESCRIPTION</h2>
      <div style="background: #f0fdf4; padding: 15px; border-left: 4px solid #22c55e; margin-bottom: 20px;">
        ${workDescription || 'Detailed project description and requirements will be specified here.'}
      </div>

      <h2 style="color: #334155; margin-top: 30px; margin-bottom: 15px;">2. COMPENSATION</h2>
      <p style="margin-bottom: 15px;">
        Payment Rate: ${paymentRate ? `$${paymentRate}` : '[Rate to be specified]'}
      </p>

      <div style="margin-top: 40px; padding: 20px; background: #f1f5f9; border-radius: 8px;">
        <p style="margin: 0; font-style: italic; color: #64748b; text-align: center;">
          Freelance agreement preview - complete all sections for full contractor protection.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generic preview for other contract types
 */
const generateSupplierAgreementPreview = (formData) => {
  return generateGenericPreview(formData, 'Supplier Agreement');
};

const generateEmploymentContractPreview = (formData) => {
  return generateGenericPreview(formData, 'Employment Contract');
};

const generateLeaseAgreementPreview = (formData) => {
  return generateGenericPreview(formData, 'Lease Agreement');
};

/**
 * Generic preview generator
 */
const generateGenericPreview = (formData, title = 'Legal Contract') => {
  const {
    contractTitle = title,
    partyOneName = '[Party 1]',
    partyTwoName = '[Party 2]',
    effectiveDate = '[Date]'
  } = formData;

  return `
    <div class="contract-content">
      <h1 style="text-align: center; margin-bottom: 30px; color: #1e293b; font-size: 24px;">
        ${contractTitle}
      </h1>
      
      <p style="margin-bottom: 20px;">
        This agreement is entered into on <strong>${effectiveDate}</strong> 
        between <strong>${partyOneName}</strong> and <strong>${partyTwoName}</strong>.
      </p>

      <div style="margin-top: 40px; padding: 20px; background: #f1f5f9; border-radius: 8px;">
        <p style="margin: 0; font-style: italic; color: #64748b; text-align: center;">
          Contract preview will appear here as you fill out the form sections.
        </p>
      </div>
    </div>
  `;
};
