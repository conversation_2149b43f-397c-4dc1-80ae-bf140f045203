import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Tooltip } from 'react-tooltip';
import {
  DocumentTextIcon,
  BeakerIcon,
  SparklesIcon,
  CodeBracketIcon,
  CogIcon,
  HomeIcon,
  BriefcaseIcon, 
} from '@heroicons/react/24/outline';

import MobileNavToggle from './ui/MobileNavToggle';
import MobileNavMenu from './ui/MobileNavMenu';
import DesktopNav from './ui/DesktopNav';
import { useAuth } from '../../context/AuthContext';

const VerticalIconNav = ({ openComingSoonModal, sidebarNavLinks = [] }) => {
  const location = useLocation();
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const { isAuthenticated } = useAuth();

  const primaryNavItems = [
    { name: 'Home / All Sections', path: '/main', icon: HomeIcon, actionType: 'navigate' },
    { name: 'PDF Tools', path: '/app/pdf', icon: DocumentTextIcon, actionType: 'navigate' },
    // ADDED: Navigation item for the Business section
    { name: 'Business Tools', path: '/app/business/create', icon: BriefcaseIcon, actionType: 'navigate' },
    { name: 'Science Solvers', icon: BeakerIcon, actionType: 'modal', tooltip: 'Science Solvers (Coming Soon)' },
    { name: 'Content Creation', icon: SparklesIcon, actionType: 'modal', tooltip: 'Content Tools (Coming Soon)' },
    { name: 'Developer Assist', icon: CodeBracketIcon, actionType: 'modal', tooltip: 'Developer Tools (Coming Soon)' },
  ];

  const bottomNavItems = [
    { name: 'Settings', path: '/settings', icon: CogIcon, actionType: 'navigate', isBottom: true },
  ];

  const isActive = (itemPath) => {
    if (!itemPath) return false;
    // MODIFIED: Ensure parent paths are highlighted correctly
    if (itemPath === '/app/pdf' || itemPath.startsWith('/app/pdf/')) {
        return location.pathname.startsWith('/app/pdf');
    }
    if (itemPath === '/app/business' || itemPath.startsWith('/app/business/')) {
      return location.pathname.startsWith('/app/business');
    }
    if (itemPath === '/app/ai-characters/list') {
      return location.pathname.startsWith('/app/ai-characters');
    }
    if (itemPath === '/main') {
      return location.pathname === '/main' || location.pathname === '/';
    }
    return location.pathname === itemPath;
  };

  const isSidebarLinkActive = (sLinkPath) => {
    return location.pathname === sLinkPath;
  };

  const handleMobileLinkClick = (actionType, path, itemName) => {
    if (actionType === 'modal') {
      openComingSoonModal(itemName);
    }
    setMobileNavOpen(false);
  };
  
  // ... (Rest of the component does not need changes)
  const getDesktopItemClasses = (itemPath, baseClass = "p-3 rounded-lg flex items-center justify-center transition-all duration-200 ease-in-out group relative") => {
    const active = isActive(itemPath);
    return `${baseClass} ${
      active
        ? 'bg-sky-500 text-white scale-110 shadow-md'
        : 'text-slate-400 hover:text-sky-400 hover:bg-sky-500/10'
    }`;
  };
  
  const desktopItemContent = (item, IconComponent, active) => (
    <>
      {active && <span className="absolute left-0 top-1/2 -translate-y-1/2 h-6 w-1 bg-sky-400 rounded-r-full"></span>}
      <IconComponent className="w-6 h-6" aria-hidden="true" />
    </>
  );

  const mobileItemContent = (item, IconOrElement) => {
    const isElement = React.isValidElement(IconOrElement);
    const IconComponent = isElement ? null : IconOrElement;

    return (
        <>
            {isElement 
                ? React.cloneElement(IconOrElement, { className: `${IconOrElement.props.className || ''} w-5 h-5 mr-3 flex-shrink-0`}) 
                : <IconComponent className="w-5 h-5 mr-3 flex-shrink-0" aria-hidden="true" />
            }
            <span className="text-sm">{item.text || item.name}</span>
        </>
    );
  };
  return (
    <>
      <MobileNavToggle onClick={() => setMobileNavOpen(true)} />
      <MobileNavMenu
        isOpen={mobileNavOpen}
        onClose={() => setMobileNavOpen(false)}
        primaryNavItems={primaryNavItems}
        sidebarNavLinks={sidebarNavLinks}
        bottomNavItems={bottomNavItems}
        isAuthenticated={isAuthenticated}
        isActive={isActive}
        isSidebarLinkActive={isSidebarLinkActive}
        handleMobileLinkClick={handleMobileLinkClick}
        mobileItemContent={mobileItemContent}
      />
      <DesktopNav
        primaryNavItems={primaryNavItems}
        bottomNavItems={bottomNavItems}
        isActive={isActive}
        getDesktopItemClasses={getDesktopItemClasses}
        desktopItemContent={desktopItemContent}
        openComingSoonModal={openComingSoonModal}
      />
      <Tooltip id="v-nav-tooltip" effect="solid" className="z-[60] !bg-slate-700 !text-xs !px-2 !py-1" />
      <div className="w-0 lg:w-16 flex-shrink-0 hidden lg:block" />
    </>
  );
};

export default VerticalIconNav;