// React hooks for Legal Contract Generator state management

import { useState, useEffect, useCallback, useRef } from 'react';
import { contractStateManager, errorHandler, validationStateManager } from '../utils/stateManager';

/**
 * Hook for managing contract state
 */
export const useContractState = () => {
  const [state, setState] = useState(contractStateManager.getState());
  const unsubscribeRef = useRef(null);

  useEffect(() => {
    // Subscribe to state changes
    unsubscribeRef.current = contractStateManager.subscribe(setState);

    // Load saved state on mount
    contractStateManager.loadSavedState();

    // Cleanup on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  const updateState = useCallback((updates) => {
    contractStateManager.setState(updates);
  }, []);

  const clearState = useCallback(() => {
    contractStateManager.clearSavedState();
  }, []);

  const undo = useCallback(() => {
    return contractStateManager.undo();
  }, []);

  const exportState = useCallback(() => {
    return contractStateManager.exportState();
  }, []);

  const importState = useCallback((importedState) => {
    return contractStateManager.importState(importedState);
  }, []);

  return {
    state,
    updateState,
    clearState,
    undo,
    exportState,
    importState,
    hasUnsavedChanges: contractStateManager.hasUnsavedChanges()
  };
};

/**
 * Hook for error handling
 */
export const useErrorHandler = () => {
  const [errors, setErrors] = useState([]);

  const logError = useCallback((error, context = {}) => {
    const errorId = errorHandler.logError(error, context);
    setErrors(errorHandler.getRecentErrors());
    return errorId;
  }, []);

  const clearErrors = useCallback(() => {
    errorHandler.clearErrors();
    setErrors([]);
  }, []);

  const getErrorStats = useCallback(() => {
    return errorHandler.getErrorStats();
  }, []);

  useEffect(() => {
    setErrors(errorHandler.getRecentErrors());
  }, []);

  return {
    errors,
    logError,
    clearErrors,
    getErrorStats
  };
};

/**
 * Hook for form validation
 */
export const useFormValidation = (contractType) => {
  const [validationState, setValidationState] = useState(validationStateManager.getValidationState());

  const validateField = useCallback((fieldName, value, context = {}) => {
    const result = validationStateManager.validateField(fieldName, value, { ...context, contractType });
    setValidationState(validationStateManager.getValidationState());
    return result;
  }, [contractType]);

  const setValidationLoading = useCallback((fieldName, isLoading) => {
    validationStateManager.setValidationState(fieldName, isLoading);
    setValidationState(validationStateManager.getValidationState());
  }, []);

  const clearValidation = useCallback((fieldName = null) => {
    validationStateManager.clearValidation(fieldName);
    setValidationState(validationStateManager.getValidationState());
  }, []);

  const isFormValid = useCallback(() => {
    return validationStateManager.isFormValid();
  }, []);

  return {
    validationState,
    validateField,
    setValidationLoading,
    clearValidation,
    isFormValid
  };
};

/**
 * Hook for auto-save functionality
 */
export const useAutoSave = (data, enabled = true, interval = 30000) => {
  const [lastSaved, setLastSaved] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const intervalRef = useRef(null);
  const lastDataRef = useRef(null);

  const save = useCallback(async () => {
    if (!enabled || JSON.stringify(data) === JSON.stringify(lastDataRef.current)) {
      return;
    }

    setIsSaving(true);
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 500));
      lastDataRef.current = data;
      setLastSaved(new Date());
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [data, enabled]);

  useEffect(() => {
    if (enabled) {
      intervalRef.current = setInterval(save, interval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [save, enabled, interval]);

  return {
    lastSaved,
    isSaving,
    save
  };
};

/**
 * Hook for managing form steps
 */
export const useFormSteps = (steps, initialStep = 0) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [stepData, setStepData] = useState({});

  const currentStep = steps[currentStepIndex];

  const goToStep = useCallback((stepIndex) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStepIndex(stepIndex);
    }
  }, [steps.length]);

  const nextStep = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStepIndex]));
      setCurrentStepIndex(prev => prev + 1);
      return true;
    }
    return false;
  }, [currentStepIndex, steps.length]);

  const prevStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
      return true;
    }
    return false;
  }, [currentStepIndex]);

  const updateStepData = useCallback((stepIndex, data) => {
    setStepData(prev => ({
      ...prev,
      [stepIndex]: { ...prev[stepIndex], ...data }
    }));
  }, []);

  const isStepCompleted = useCallback((stepIndex) => {
    return completedSteps.has(stepIndex);
  }, [completedSteps]);

  const markStepCompleted = useCallback((stepIndex) => {
    setCompletedSteps(prev => new Set([...prev, stepIndex]));
  }, []);

  const resetSteps = useCallback(() => {
    setCurrentStepIndex(initialStep);
    setCompletedSteps(new Set());
    setStepData({});
  }, [initialStep]);

  return {
    currentStepIndex,
    currentStep,
    stepData,
    completedSteps,
    goToStep,
    nextStep,
    prevStep,
    updateStepData,
    isStepCompleted,
    markStepCompleted,
    resetSteps,
    isFirstStep: currentStepIndex === 0,
    isLastStep: currentStepIndex === steps.length - 1,
    progress: ((currentStepIndex + 1) / steps.length) * 100
  };
};

/**
 * Hook for debounced values
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Hook for local storage with expiration
 */
export const useLocalStorageWithExpiry = (key, defaultValue, expiryTime = 3600000) => {
  const [value, setValue] = useState(() => {
    try {
      const item = localStorage.getItem(key);
      if (!item) return defaultValue;

      const parsed = JSON.parse(item);
      if (parsed.expiresAt && Date.now() > parsed.expiresAt) {
        localStorage.removeItem(key);
        return defaultValue;
      }

      return parsed.data;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return defaultValue;
    }
  });

  const setStoredValue = useCallback((newValue) => {
    try {
      setValue(newValue);
      const item = {
        data: newValue,
        expiresAt: Date.now() + expiryTime
      };
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  }, [key, expiryTime]);

  const removeStoredValue = useCallback(() => {
    try {
      setValue(defaultValue);
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }, [key, defaultValue]);

  return [value, setStoredValue, removeStoredValue];
};
